"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  PasswordResetRequestFormData,
  passwordResetRequestSchema,
} from "@/lib/account-schemas";
import { authUtils } from "@/lib/auth-utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { AlertCircle, ArrowLeft, Mail } from "lucide-react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

interface ForgotPasswordFormProps {
  onBack: () => void;
  prefillEmail?: string;
}

export function ForgotPasswordForm({
  onBack,
  prefillEmail,
}: ForgotPasswordFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    getValues,
    setValue,
  } = useForm<PasswordResetRequestFormData>({
    resolver: zodResolver(passwordResetRequestSchema),
    defaultValues: {
      email: prefillEmail || "",
    },
  });

  useEffect(() => {
    if (prefillEmail) {
      setValue("email", prefillEmail);
    }
  }, [prefillEmail, setValue]);

  const handleFormSubmit = async (data: PasswordResetRequestFormData) => {
    setIsLoading(true);
    try {
      await authUtils.requestPasswordReset(data);
      setIsSuccess(true);
      toast.success(
        "Dacă adresa de email există în sistem și are parolă setată, veți primi instrucțiunile de resetare."
      );
    } catch (error) {
      console.error("Password reset request error:", error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : "A apărut o eroare la solicitarea resetării parolei";
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  if (isSuccess) {
    return (
      <div className="space-y-6">
        <div className="text-center space-y-2">
          <div className="mx-auto w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
            <Mail className="h-6 w-6 text-green-600 dark:text-green-400" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Solicitare procesată!
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Dacă adresa{" "}
            <span className="font-medium text-gray-900 dark:text-white">
              {getValues("email")}
            </span>{" "}
            există în sistem și are parolă setată, veți primi instrucțiunile de
            resetare.
          </p>
        </div>

        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <AlertCircle className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5" />
            <div className="text-sm text-blue-800 dark:text-blue-200">
              <p className="font-medium mb-1">Verifică-ți inbox-ul</p>
              <ul className="space-y-1 text-blue-700 dark:text-blue-300">
                <li>• Link-ul este valabil doar 1 oră</li>
                <li>• Verifică și folderul de spam</li>
                <li>• Dacă nu primești email-ul, încearcă din nou</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="space-y-3">
          <Button
            onClick={onBack}
            variant="outline"
            className="w-full"
            type="button"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Înapoi la Portavio
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
          Ai uitat parola?
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Introdu adresa de email și îți vom trimite instrucțiunile pentru
          resetarea parolei.
        </p>
      </div>

      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="email">Adresa de email</Label>
          <Input
            id="email"
            type="email"
            placeholder="<EMAIL>"
            {...register("email")}
            disabled={isLoading}
            className={errors.email ? "border-red-500" : ""}
          />
          {errors.email && (
            <p className="text-sm text-red-500 flex items-center gap-1">
              <AlertCircle className="h-4 w-4" />
              {errors.email.message}
            </p>
          )}
        </div>

        <div className="space-y-3">
          <Button type="submit" disabled={isLoading} className="w-full">
            {isLoading ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Se trimite...
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                Trimite instrucțiunile
              </div>
            )}
          </Button>

          <Button
            onClick={onBack}
            variant="outline"
            className="w-full"
            type="button"
            disabled={isLoading}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Înapoi la Portavio
          </Button>
        </div>
      </form>
    </div>
  );
}
