import { hasuraQuery } from "./hasura";
import { SupportedCurrency } from "@/components/dashboard/currency-selector";
import {
  createExternalAPIClient,
  ExternalAPIError,
  AssetWithMetricsResponse,
} from "@/lib/external-api-client";

// Types for exchange rate data
export interface ExchangeRate {
  fromCurrency: string;
  toCurrency: string;
  rate: number;
  date: string;
}

export type ExchangeRateMap = Map<string, number>;

// GraphQL query to get exchange rates from asset_price table
export const GET_EXCHANGE_RATES = `
  query GetExchangeRates($tickers: [String!]!) {
    ptvuser_asset(where: {ticker: {_in: $tickers}}) {
      ticker
      latest_price: asset_prices(
        order_by: {date: desc}
        limit: 1
      ) {
        close_price: adj_close
        date
      }
    }
  }
`;

// GraphQL query to check if exchange rate assets exist
export const GET_EXCHANGE_RATE_ASSETS = `
  query GetExchangeRateAssets($tickers: [String!]!) {
    ptvuser_asset(where: {ticker: {_in: $tickers}}) {
      asset_id
      ticker
      name
    }
  }
`;

/**
 * Generate currency pair ticker for EODHD format with .FOREX suffix
 * Examples: EURUSD.FOREX, USDEUR.FOREX, EURRON.FOREX, RONEUR.FOREX
 */
export function generateCurrencyPairTicker(
  fromCurrency: string,
  toCurrency: string
): string {
  return `${fromCurrency.toUpperCase()}${toCurrency.toUpperCase()}.FOREX`;
}

/**
 * Get all required exchange rate pairs for portfolio conversion
 */
export function getRequiredExchangeRates(
  portfolioCurrencies: string[],
  targetCurrency: SupportedCurrency
): string[] {
  const uniqueCurrencies = Array.from(new Set(portfolioCurrencies));
  const requiredPairs: string[] = [];

  for (const currency of uniqueCurrencies) {
    if (currency !== targetCurrency) {
      // Direct conversion (e.g., USD to EUR = USDEUR)
      const directPair = generateCurrencyPairTicker(currency, targetCurrency);
      requiredPairs.push(directPair);

      // Also add reverse pair for fallback (e.g., EUR to USD = EURUSD)
      const reversePair = generateCurrencyPairTicker(targetCurrency, currency);
      requiredPairs.push(reversePair);
    }
  }

  console.log("required: ", requiredPairs);

  return Array.from(new Set(requiredPairs)); // Remove duplicates
}

/**
 * Fetch exchange rates from database
 */
export async function fetchExchangeRatesFromDB(
  currencyPairs: string[]
): Promise<ExchangeRateMap> {
  const exchangeRates = new Map<string, number>();

  if (currencyPairs.length === 0) {
    return exchangeRates;
  }

  try {
    const result = await hasuraQuery<{
      ptvuser_asset: Array<{
        ticker: string;
        latest_price: Array<{
          close_price: number;
          date: string;
        }>;
      }>;
    }>(GET_EXCHANGE_RATES, {
      variables: { tickers: currencyPairs },
    });

    result.ptvuser_asset?.forEach((asset) => {
      if (asset.latest_price?.[0]?.close_price) {
        exchangeRates.set(asset.ticker, asset.latest_price[0].close_price);
      }
    });

    return exchangeRates;
  } catch (error) {
    console.error("Error fetching exchange rates from database:", error);
    return exchangeRates;
  }
}

/**
 * Check which exchange rate assets exist in database
 */
export async function checkExchangeRateAssetsExist(
  currencyPairs: string[]
): Promise<Set<string>> {
  const existingAssets = new Set<string>();

  if (currencyPairs.length === 0) {
    return existingAssets;
  }

  try {
    const result = await hasuraQuery<{
      ptvuser_asset: Array<{
        asset_id: number;
        ticker: string;
        name: string;
      }>;
    }>(GET_EXCHANGE_RATE_ASSETS, {
      variables: { tickers: currencyPairs },
    });

    result.ptvuser_asset?.forEach((asset) => {
      existingAssets.add(asset.ticker);
    });

    return existingAssets;
  } catch (error) {
    console.error("Error checking exchange rate assets:", error);
    return existingAssets;
  }
}

/**
 * Fetch missing exchange rates from EODHD API
 * Uses ExternalAPIClient to properly fetch exchange rate data
 */
export async function fetchMissingExchangeRates(
  missingPairs: string[]
): Promise<void> {
  if (missingPairs.length === 0) {
    return;
  }

  try {
    // Create API client instance
    const apiClient = createExternalAPIClient();

    // Fetch exchange rates with limited history (recent rates only)
    const promises = missingPairs.map(async (ticker) => {
      try {
        if (process.env.NODE_ENV === "development") {
          console.log(`Fetching exchange rate data for ${ticker}...`);
        }

        console.log(`Fetching exchange rate data for ${ticker}...`);

        // Ticker already includes .FOREX suffix from generateCurrencyPairTicker
        // Use getAssetHistory with minimal days for exchange rates
        const result: AssetWithMetricsResponse =
          await apiClient.getAssetHistory(ticker, {
            refresh: true, // Always refresh for exchange rates
            useMock: false,
            daysHistory: 2, // Get last 2 days to ensure we have recent data
            providers: [],
          });

        if (
          result.asset &&
          result.asset.prices &&
          result.asset.prices.length > 0
        ) {
          if (process.env.NODE_ENV === "development") {
            console.log(
              `Successfully fetched exchange rate data for ${ticker} - ${result.asset.prices.length} price points`
            );
          }
        } else {
          console.warn(`No price data returned for exchange rate ${ticker}`);
        }

        return result;
      } catch (error) {
        if (error instanceof ExternalAPIError) {
          if (error.statusCode === 404) {
            console.warn(`Exchange rate ${ticker} not found in EODHD API`);
          } else {
            console.error(
              `API error fetching exchange rate for ${ticker}:`,
              error.message
            );
          }
        } else {
          console.error(`Error fetching exchange rate for ${ticker}:`, error);
        }
        return null;
      }
    });

    // Wait for all exchange rate fetches to complete
    const results = await Promise.allSettled(promises);

    // Log summary
    const successful = results.filter(
      (result) => result.status === "fulfilled" && result.value !== null
    ).length;

    if (process.env.NODE_ENV === "development") {
      console.log(
        `Exchange rate fetch completed: ${successful}/${missingPairs.length} successful`
      );
    }
  } catch (error) {
    console.error("Error in fetchMissingExchangeRates:", error);
    throw new Error("Nu s-au putut obține cursurile de schimb lipsă");
  }
}

/**
 * Convert amount from one currency to another
 */
export function convertAmount(
  amount: number,
  fromCurrency: string,
  toCurrency: string,
  exchangeRates: ExchangeRateMap
): number {
  // If same currency, no conversion needed
  if (fromCurrency === toCurrency) {
    return amount;
  }

  console.log(`Converting ${amount} ${fromCurrency} to ${toCurrency}`);

  // Try direct conversion (e.g., USD to EUR using USDEUR rate)
  const directPair = generateCurrencyPairTicker(fromCurrency, toCurrency);
  const directRate = exchangeRates.get(directPair);

  if (directRate && directRate > 0) {
    const convertedAmount = amount * directRate;

    return convertedAmount;
  }

  // Try inverse conversion (e.g., USD to EUR using EURUSD rate = 1/rate)
  const inversePair = generateCurrencyPairTicker(toCurrency, fromCurrency);
  const inverseRate = exchangeRates.get(inversePair);

  if (inverseRate && inverseRate > 0) {
    const convertedAmount = amount / inverseRate;

    return convertedAmount;
  }

  // If no exchange rate available, return original amount

  console.warn(`No exchange rate found for ${fromCurrency} to ${toCurrency}`);
  return amount;
}

/**
 * Get all unique currencies from portfolio transactions
 */
export function extractPortfolioCurrencies(
  assetData: Map<string, any>
): string[] {
  const currencies = new Set<string>();

  assetData.forEach((asset) => {
    if (asset?.currency?.code) {
      currencies.add(asset.currency.code);
    } else if (asset?.currency?.name) {
      currencies.add(asset.currency.name);
    }
  });

  return Array.from(currencies);
}
