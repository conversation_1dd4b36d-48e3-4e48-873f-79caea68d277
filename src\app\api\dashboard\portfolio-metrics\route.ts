import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { getPortfolioMetricsData } from "@/utils/db/dashboard-queries";
import { SupportedCurrency } from "@/components/dashboard/currency-selector";
import { headers } from "next/headers";

export async function GET(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Nu sunteți autentificat" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const portfolioIdsParam = searchParams.get("portfolioIds");
    const displayCurrency = (searchParams.get("displayCurrency") ||
      "EUR") as SupportedCurrency;

    if (!portfolioIdsParam) {
      return NextResponse.json(
        { error: "Portfolio IDs sunt necesare" },
        { status: 400 }
      );
    }

    const portfolioIds = portfolioIdsParam.split(",").filter(Boolean);

    const data = await getPortfolioMetricsData(portfolioIds, displayCurrency);

    return NextResponse.json({
      success: true,
      data,
      message: "Datele de metrici au fost încărcate cu succes",
    });
  } catch (error) {
    console.error("Error in portfolio metrics API:", error);
    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : "Eroare la încărcarea datelor de metrici",
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Nu sunteți autentificat" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { portfolioIds, displayCurrency = "EUR" } = body;

    if (!portfolioIds || !Array.isArray(portfolioIds)) {
      return NextResponse.json(
        { error: "Portfolio IDs sunt necesare și trebuie să fie un array" },
        { status: 400 }
      );
    }

    const data = await getPortfolioMetricsData(
      portfolioIds,
      displayCurrency as SupportedCurrency
    );

    return NextResponse.json({
      success: true,
      data,
      message: "Datele de metrici au fost încărcate cu succes",
    });
  } catch (error) {
    console.error("Error in portfolio metrics API:", error);
    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : "Eroare la încărcarea datelor de metrici",
      },
      { status: 500 }
    );
  }
}
