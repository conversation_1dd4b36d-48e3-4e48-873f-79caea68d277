-- Add transaction_fee column to ptvuser_transactions table
-- This column will store the optional transaction fee/commission for each transaction

-- Add the transaction_fee column as optional (nullable)
ALTER TABLE ptvuser.transactions 
ADD COLUMN IF NOT EXISTS transaction_fee DECIMAL(15,8);

-- Add comment to the new column
COMMENT ON COLUMN ptvuser.transactions.transaction_fee IS 'Optional transaction fee/commission amount (supports up to 8 decimal places)';

-- Note: The column is intentionally nullable since transaction fees are optional
-- Existing transactions will have NULL values for transaction_fee, which is the desired behavior
