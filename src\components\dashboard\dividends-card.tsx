"use client";

import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON> } from "@/components/ui/bar-chart";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { usePortfolioDividends } from "@/hooks/use-portfolio-dividends";
import { SupportedCurrency } from "./currency-selector";
import { DividendTableRow } from "@/utils/db/dashboard-queries";
import { Loader2, ChevronDown, Calendar } from "lucide-react";

interface DividendsCardProps {
  selectedPortfolios: string[];
  displayCurrency: SupportedCurrency;
}

const formatCurrency = (value: number, currency: SupportedCurrency): string => {
  return new Intl.NumberFormat("ro-RO", {
    style: "currency",
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value);
};

// const formatPercentage = (value: number): string => {
//   return `${value.toFixed(2)}%`;
// };

export function DividendsCard({
  selectedPortfolios,
  displayCurrency,
}: DividendsCardProps) {
  const {
    data: dividendsData,
    isLoading,
    error,
  } = usePortfolioDividends(selectedPortfolios, displayCurrency);

  const [selectedYear, setSelectedYear] = useState<number>(
    new Date().getFullYear()
  );

  if (isLoading) {
    return (
      <Card className="lg:col-span-2">
        <CardHeader>
          <CardTitle className="text-xl">Dividende</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            <div className="flex items-center gap-2 text-muted-foreground">
              <Loader2 className="h-4 w-4 animate-spin" />
              Se încarcă datele...
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="lg:col-span-2">
        <CardHeader>
          <CardTitle className="text-xl">Dividende</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            <div className="text-center text-muted-foreground">
              <div className="text-lg font-medium mb-2">
                Eroare la încărcarea datelor
              </div>
              <div className="text-sm">{error}</div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!dividendsData || dividendsData.yearlyData.length === 0) {
    return (
      <Card className="lg:col-span-2">
        <CardHeader>
          <CardTitle className="text-xl">Dividende</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            <div className="text-center text-muted-foreground">
              <div className="text-lg font-medium mb-2">
                Nu există date de dividende
              </div>
              <div className="text-sm">
                Selectați un portofoliu cu tranzacții pentru a vedea dividendele
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Prepare data for stacked bar chart
  const chartData = dividendsData.yearlyData.map((yearData) => {
    const dataPoint: any = { year: yearData.year.toString() };
    yearData.assetBreakdown.forEach((asset) => {
      dataPoint[asset.ticker] = asset.amount;
    });
    return dataPoint;
  });

  // Get all unique tickers for chart categories
  const allTickers = [
    ...new Set(
      dividendsData.yearlyData.flatMap((year) =>
        year.assetBreakdown.map((asset) => asset.ticker)
      )
    ),
  ];

  // Use default diverse color palette for better visual contrast
  // This provides better differentiation between companies in stacked bars

  // Get available years for dropdown
  const availableYears = dividendsData.yearlyData
    .map((year) => year.year)
    .sort((a, b) => b - a);

  // Get table data for selected year
  const tableData = dividendsData.tableData[selectedYear] || [];

  return (
    <Card className="lg:col-span-2">
      <CardHeader>
        <CardTitle className="text-xl">Dividende</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Top Metrics Section */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-muted/50 rounded-lg p-4">
            <div className="text-2xl font-bold text-portavio-blue">
              {formatCurrency(dividendsData.totalDividends, displayCurrency)}
            </div>
            <div className="text-sm text-muted-foreground">Totalul primit</div>
          </div>
          <div className="bg-muted/50 rounded-lg p-4">
            <div className="text-2xl font-bold text-portavio-blue">
              {/* {formatPercentage(dividendsData.dividendYieldTTM)} */}
              N/A
            </div>
            <div className="text-sm text-muted-foreground">
              Dividend Yield (TTM)
            </div>
          </div>
          <div className="bg-muted/50 rounded-lg p-4">
            <div className="text-2xl font-bold text-portavio-blue">
              {/* {formatPercentage(dividendsData.yocTTM)} */}
              N/A
            </div>
            <div className="text-sm text-muted-foreground">YoC (TTMM)</div>
          </div>
          <div className="bg-muted/50 rounded-lg p-4">
            <div className="text-2xl font-bold text-portavio-blue">
              {/* {formatPercentage(dividendsData.cagrPayouts)} */}
              N/A
            </div>
            <div className="text-sm text-muted-foreground">CAGR (Payouts)</div>
          </div>
        </div>

        {/* Bar Charts Section */}
        <div className="space-y-4">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Calendar className="h-4 w-4" />
            <span>Dividende pe ani (ultimii 6 ani)</span>
          </div>
          {chartData.length > 0 ? (
            <BarChart
              data={chartData}
              index="year"
              categories={allTickers}
              valueFormatter={(value) => formatCurrency(value, displayCurrency)}
              className="h-80"
              showLegend={true}
              showTooltip={true}
              type="stacked"
            />
          ) : (
            <div className="h-80 flex items-center justify-center text-muted-foreground">
              <div className="text-center">
                <div className="text-lg font-medium mb-2">
                  Nu există date de dividende
                </div>
                <div className="text-sm">
                  Pentru perioada selectată nu au fost găsite dividende
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Table Section */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Detalii dividende</h3>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="w-32">
                  {selectedYear}
                  <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                {availableYears.map((year) => (
                  <DropdownMenuItem
                    key={year}
                    onClick={() => setSelectedYear(year)}
                  >
                    {year}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {tableData.length > 0 ? (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Asset</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Payments</TableHead>
                    <TableHead className="text-right">Payout</TableHead>
                    {/* <TableHead className="text-right">Yield</TableHead>
                    <TableHead className="text-right">YoY growth</TableHead> */}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {tableData.map((row: DividendTableRow) => (
                    <TableRow key={row.ticker}>
                      <TableCell>
                        <div className="text-left">
                          <div className="font-medium">{row.company}</div>
                          <div className="text-sm text-muted-foreground text-left">
                            {row.ticker}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="text-left">
                        <span className="inline-flex items-center rounded-full px-2 py-1 text-xs font-medium bg-green-100 text-green-800">
                          {row.status}
                        </span>
                      </TableCell>
                      <TableCell className="text-right">
                        {row.payments}
                      </TableCell>
                      <TableCell className="text-right">
                        {formatCurrency(row.payout, displayCurrency)}
                      </TableCell>
                      {/* <TableCell className="text-right">
                        {formatPercentage(row.yield)}
                      </TableCell>
                      <TableCell className="text-right">
                        {formatPercentage(row.yoyGrowth)}
                      </TableCell> */}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <div className="text-lg font-medium mb-2">
                Nu există dividende pentru {selectedYear}
              </div>
              <div className="text-sm">
                Selectați un alt an din dropdown-ul de mai sus
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
