import { hasuraQuery } from "@/utils/db";
import { sendWelcomeEmailIfNeeded } from "@/utils/db/welcome-email-queries";

/**
 * Check if a user has Google OAuth account
 */
async function hasGoogleAccount(userId: string): Promise<boolean> {
  try {
    const query = `
      query CheckGoogleAccount($userId: String!) {
        ptvuser_account(
          where: {
            userId: {_eq: $userId}, 
            providerId: {_eq: "google"}
          }, 
          limit: 1
        ) {
          id
          providerId
        }
      }
    `;

    const result = await hasuraQuery<{
      ptvuser_account: Array<{
        id: string;
        providerId: string;
      }>;
    }>(query, { variables: { userId } });

    return result.ptvuser_account?.length > 0;
  } catch (error) {
    console.error("Error checking Google account:", error);
    return false;
  }
}

/**
 * Handle Google OAuth welcome email logic using database-driven approach
 * This uses the welcomeEmailSent field to determine if welcome email should be sent
 */
export async function handleGoogleOAuthWelcome(
  userEmail: string,
  userName?: string,
  userId?: string
): Promise<{ success: boolean; message: string; emailSent: boolean }> {
  try {
    // First, verify this user has a Google account (required for Google OAuth welcome)
    if (!userId) {
      return {
        success: true,
        message: "No user ID provided, skipping welcome email check",
        emailSent: false,
      };
    }

    const hasGoogle = await hasGoogleAccount(userId);
    if (!hasGoogle) {
      return {
        success: true,
        message: "User doesn't have Google account, no welcome email needed",
        emailSent: false,
      };
    }

    // Use database-driven welcome email system
    const welcomeResult = await sendWelcomeEmailIfNeeded(
      userId,
      userEmail,
      userName
    );

    if (welcomeResult.emailSent) {
      console.log(`Welcome email sent to Google OAuth user: ${userEmail}`);
    } else if (welcomeResult.alreadySent) {
      console.log(
        `Welcome email already sent to Google OAuth user: ${userEmail}`
      );
    }

    return {
      success: welcomeResult.success,
      message: welcomeResult.message,
      emailSent: welcomeResult.emailSent,
    };
  } catch (error) {
    console.error("Error handling Google OAuth welcome:", error);
    return {
      success: false,
      message: "Failed to send welcome email",
      emailSent: false,
    };
  }
}
