-- Application-Specific Tables
-- Tables for application features like profile pictures, search cache, contact forms, etc.

-- Create profile pictures table to store base64 images (one per user)
CREATE TABLE IF NOT EXISTS ptvuser.profile_pictures (
    user_id TEXT PRIMARY KEY REFERENCES "user"(id) ON DELETE CASCADE,
    image_data TEXT NOT NULL, -- Base64 encoded image data
    image_type VARCHAR(50) NOT NULL DEFAULT 'image/jpeg', -- MIME type (image/jpeg, image/png, etc.)
    file_size INTEGER NOT NULL, -- Size in bytes for validation
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    CONSTRAINT profile_pictures_image_type_check CHECK (image_type IN ('image/jpeg', 'image/jpg', 'image/png', 'image/webp'))
);

-- Create search_results table to cache EODHD API search results for improved performance
CREATE TABLE IF NOT EXISTS ptvuser.search_results (
    id SERIAL PRIMARY KEY,
    code VARCHAR(50) NOT NULL, -- Asset code/ticker (e.g., "AAPL", "AA")
    exchange VARCHAR(10) NOT NULL, -- Exchange code (e.g., "US", "LSE", "VN")
    name VARCHAR(500) NOT NULL, -- Asset name (e.g., "Apple Inc", "Alcoa Corp")
    type VARCHAR(100) NOT NULL, -- Asset type (e.g., "Common Stock", "ETF", "Currency")
    country VARCHAR(100) NOT NULL, -- Country (e.g., "USA", "UK", "Vietnam")
    currency VARCHAR(10) NOT NULL, -- Currency code (e.g., "USD", "GBX", "VND")
    isin VARCHAR(20), -- ISIN code (nullable since not all assets have ISIN)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Composite unique constraint to prevent duplicates (same code + exchange combination)
    CONSTRAINT search_results_code_exchange_unique UNIQUE (code, exchange)
);

-- Create contact submissions table
CREATE TABLE IF NOT EXISTS ptvuser_contact_submissions (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    surname VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    ip_address INET,
    user_agent TEXT,
    status VARCHAR(50) DEFAULT 'new' CHECK (status IN ('new', 'read', 'replied', 'archived')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Email validation constraint
    CONSTRAINT ptvuser_contact_submissions_email_check CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- Create support FAQ table
CREATE TABLE IF NOT EXISTS support_faq (
    id SERIAL PRIMARY KEY,
    question VARCHAR(500) NOT NULL,
    answer TEXT NOT NULL,
    is_active BOOLEAN DEFAULT true,
    display_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_search_results_code ON ptvuser.search_results(code);
CREATE INDEX IF NOT EXISTS idx_search_results_exchange ON ptvuser.search_results(exchange);
CREATE INDEX IF NOT EXISTS idx_search_results_name ON ptvuser.search_results(name);
CREATE INDEX IF NOT EXISTS idx_search_results_type ON ptvuser.search_results(type);
CREATE INDEX IF NOT EXISTS idx_search_results_country ON ptvuser.search_results(country);
CREATE INDEX IF NOT EXISTS idx_search_results_currency ON ptvuser.search_results(currency);
CREATE INDEX IF NOT EXISTS idx_search_results_created_at ON ptvuser.search_results(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_ptvuser_contact_submissions_created_at ON ptvuser_contact_submissions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_ptvuser_contact_submissions_status ON ptvuser_contact_submissions(status);
CREATE INDEX IF NOT EXISTS idx_ptvuser_contact_submissions_email ON ptvuser_contact_submissions(email);
CREATE INDEX IF NOT EXISTS idx_ptvuser_contact_submissions_ip_address ON ptvuser_contact_submissions(ip_address);

CREATE INDEX IF NOT EXISTS idx_support_faq_is_active ON support_faq(is_active);
CREATE INDEX IF NOT EXISTS idx_support_faq_display_order ON support_faq(display_order);

-- Create updated_at triggers
CREATE TRIGGER update_profile_pictures_updated_at
    BEFORE UPDATE ON ptvuser.profile_pictures
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_search_results_updated_at
    BEFORE UPDATE ON ptvuser.search_results
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ptvuser_contact_submissions_updated_at
    BEFORE UPDATE ON ptvuser_contact_submissions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_support_faq_updated_at
    BEFORE UPDATE ON support_faq
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE ptvuser.profile_pictures IS 'Stores user profile pictures as base64 encoded data (one per user)';
COMMENT ON COLUMN ptvuser.profile_pictures.user_id IS 'Primary key - reference to the user who owns this profile picture';
COMMENT ON COLUMN ptvuser.profile_pictures.image_data IS 'Base64 encoded image data';
COMMENT ON COLUMN ptvuser.profile_pictures.image_type IS 'MIME type of the image (image/jpeg, image/png, etc.)';
COMMENT ON COLUMN ptvuser.profile_pictures.file_size IS 'Size of the original file in bytes for validation';

COMMENT ON TABLE ptvuser.search_results IS 'Cache table for EODHD API search results to improve search performance';
COMMENT ON COLUMN ptvuser.search_results.code IS 'Asset code/ticker symbol';
COMMENT ON COLUMN ptvuser.search_results.exchange IS 'Exchange code where the asset is traded';
COMMENT ON COLUMN ptvuser.search_results.name IS 'Full name of the asset';
COMMENT ON COLUMN ptvuser.search_results.type IS 'Type of asset (Common Stock, ETF, Currency, etc.)';
COMMENT ON COLUMN ptvuser.search_results.country IS 'Country where the asset is based';
COMMENT ON COLUMN ptvuser.search_results.currency IS 'Currency in which the asset is traded';
COMMENT ON COLUMN ptvuser.search_results.isin IS 'International Securities Identification Number (nullable)';

COMMENT ON TABLE ptvuser_contact_submissions IS 'Stores contact form submissions from the website';
COMMENT ON COLUMN ptvuser_contact_submissions.name IS 'First name of the person submitting the form';
COMMENT ON COLUMN ptvuser_contact_submissions.surname IS 'Last name of the person submitting the form';
COMMENT ON COLUMN ptvuser_contact_submissions.email IS 'Email address of the person submitting the form';
COMMENT ON COLUMN ptvuser_contact_submissions.message IS 'The message content from the contact form';
COMMENT ON COLUMN ptvuser_contact_submissions.ip_address IS 'IP address of the person submitting the form (for rate limiting)';
COMMENT ON COLUMN ptvuser_contact_submissions.user_agent IS 'Browser user agent string';
COMMENT ON COLUMN ptvuser_contact_submissions.status IS 'Status of the contact submission (new, read, replied, archived)';

COMMENT ON TABLE support_faq IS 'Frequently asked questions for the support section';
COMMENT ON COLUMN support_faq.question IS 'The FAQ question';
COMMENT ON COLUMN support_faq.answer IS 'The FAQ answer';
COMMENT ON COLUMN support_faq.is_active IS 'Whether this FAQ is currently active/visible';
COMMENT ON COLUMN support_faq.display_order IS 'Order in which to display this FAQ';

-- Insert default FAQ data
INSERT INTO support_faq (question, answer, display_order) VALUES
(
    'Cum mă ajută această aplicație să îmi gestionez investițiile?',
    'Aplicația Portavio vă oferă o platformă centralizată pentru monitorizarea și gestionarea portofoliului de investiții. Prin conectarea automată cu brokerul dumneavoastră, puteți urmări în timp real performanța investițiilor, analiza distribuția activelor și lua decizii informate. Platforma oferă grafice interactive, rapoarte detaliate și alerte personalizate pentru a vă ține la curent cu evoluția investițiilor.',
    1
),
(
    'Sunt datele mele financiare în siguranță?',
    'Da! Utilizăm criptare la nivel bancar și protocoale de securitate standard din industrie pentru a vă proteja datele. Informațiile dumneavoastră personale și financiare nu sunt niciodată partajate cu terțe părți, iar dumneavoastră aveți control complet asupra conturilor conectate și setărilor de confidențialitate. Toate datele sunt stocate în servere securizate și sunt supuse unor verificări regulate de securitate.',
    2
),
(
    'Oferă aplicația sfaturi de investiții sau recomandări?',
    'Nu, aplicația Portavio nu oferă sfaturi de investiții sau recomandări financiare. Suntem o platformă de monitorizare și analiză a portofoliului care vă ajută să vizualizați și să înțelegeți mai bine investițiile existente. Pentru sfaturi de investiții, vă recomandăm să consultați un consilier financiar autorizat. Platforma noastră vă oferă instrumentele necesare pentru a analiza datele, dar deciziile de investiții rămân în totalitate ale dumneavoastră.',
    3
) ON CONFLICT DO NOTHING;
