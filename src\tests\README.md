# Dashboard Calculation Functions Test Suite

This directory contains comprehensive tests for all mathematical calculation functions used in the Portavio dashboard. The tests are designed to run standalone with Node.js without requiring external testing frameworks.

## 📁 Test Files

### Core Calculation Tests

1. **`test-portfolio-holdings-at-date.js`**
   - Tests `calculatePortfolioHoldingsAtDate` function
   - Verifies correct share quantities and cost basis at specific dates
   - Covers BUY/SELL transaction logic and edge cases

2. **`test-ticker-dividend-income.js`**
   - Tests `calculateTickerDividendIncome` function
   - Validates ex-date based dividend calculations
   - Ensures proper currency conversion for dividends

3. **`test-portfolio-composition.js`**
   - Tests `calculatePortfolioComposition` function
   - Verifies portfolio breakdown by sectors, industries, currencies, countries
   - Validates percentage calculations and total value computation

4. **`test-portfolio-metrics.js`**
   - Tests `calculatePortfolioMetrics` function
   - Covers capital invested, price gains, dividend income calculations
   - Validates return percentages and transaction costs

5. **`test-company-holdings.js`**
   - Tests `calculateCompanyHoldings` function
   - Verifies net share calculations after BUY/SELL transactions
   - Tests filtering of fully sold positions

6. **`test-currency-conversion.js`**
   - Tests currency conversion functions
   - Validates exchange rate applications and fallback mechanisms
   - Covers multi-currency portfolio scenarios

## 🚀 Running Tests

### Run All Tests
```bash
node src/tests/run-all-tests.js
```

### Run Individual Tests
```bash
node src/tests/test-portfolio-holdings-at-date.js
node src/tests/test-ticker-dividend-income.js
node src/tests/test-portfolio-composition.js
node src/tests/test-portfolio-metrics.js
node src/tests/test-company-holdings.js
node src/tests/test-currency-conversion.js
```

## 📊 Test Coverage

### Functions Tested

| Function | Test File | Coverage |
|----------|-----------|----------|
| `calculatePortfolioHoldingsAtDate` | `test-portfolio-holdings-at-date.js` | ✅ Complete |
| `calculateTickerDividendIncome` | `test-ticker-dividend-income.js` | ✅ Complete |
| `calculatePortfolioComposition` | `test-portfolio-composition.js` | ✅ Complete |
| `calculatePortfolioMetrics` | `test-portfolio-metrics.js` | ✅ Complete |
| `calculateCompanyHoldings` | `test-company-holdings.js` | ✅ Complete |
| `convertAmount` | `test-currency-conversion.js` | ✅ Complete |
| `extractPortfolioCurrencies` | `test-currency-conversion.js` | ✅ Complete |
| `getRequiredExchangeRates` | `test-currency-conversion.js` | ✅ Complete |

### Edge Cases Covered

- **Empty portfolios and transactions**
- **Single transaction scenarios**
- **Multiple currency conversions**
- **Dividend ex-dates before/after holdings**
- **BUY/SELL transaction combinations**
- **Fully sold positions**
- **Missing exchange rates**
- **Zero and negative amounts**
- **Large number precision**
- **Round-trip conversions**

## 🧪 Test Data Structure

All tests use realistic mock data that mirrors the actual database schema:

### Transaction Structure
```javascript
{
  id: "1",
  ticker: "AAPL",
  transaction_type: "BUY", // or "SELL"
  quantity: 10,
  price: 150.00,
  transaction_date: "2024-01-15",
  transaction_fee: 5.00
}
```

### Asset Data Structure
```javascript
{
  name: "Apple Inc.",
  currency: { code: "USD" },
  sector: { name: "Technology" },
  industry: { name: "Consumer Electronics" },
  country: { name: "United States" },
  asset_type: { name: "Stock" }
}
```

### Dividend Structure
```javascript
{
  dividend_id: 1,
  asset_id: 213,
  ex_date: "2024-02-01",
  amount_per_share: 0.25,
  dividend_type: "Regular",
  status: "Paid",
  asset: { 
    ticker: "AAPL", 
    currency: { code: "USD" } 
  }
}
```

## 📈 Test Output

Each test provides detailed output showing:
- ✅ **PASS** for successful tests
- ❌ **FAIL** for failed tests with expected vs actual values
- Detailed calculation breakdowns
- Summary statistics

### Example Output
```
🧪 Testing calculatePortfolioHoldingsAtDate function

Test 1: Holdings before any transactions
✅ PASS: Empty holdings before transactions

Test 2: Holdings after first AAPL purchase
✅ PASS: AAPL holdings after first purchase

🏁 Test suite completed!
```

## 🔧 Test Architecture

### Design Principles
1. **Standalone Execution** - No external dependencies
2. **Realistic Data** - Mirrors actual database structures
3. **Comprehensive Coverage** - Tests normal and edge cases
4. **Clear Output** - Easy to understand pass/fail results
5. **Mathematical Precision** - Validates calculations with appropriate tolerance

### Mock Functions
Each test file includes mock implementations of the functions being tested, ensuring tests can run independently without requiring the full application context.

## 🎯 Validation Strategy

### Mathematical Accuracy
- All monetary calculations tested with precision tolerance (±0.01)
- Percentage calculations validated to appropriate decimal places
- Currency conversions tested with known exchange rates

### Business Logic
- Transaction ordering and date-based calculations
- Portfolio composition percentages sum to 100%
- Dividend calculations respect ex-date holdings
- Fully sold positions properly excluded

### Error Handling
- Missing data scenarios
- Invalid input handling
- Fallback mechanisms for missing exchange rates

## 📝 Adding New Tests

To add tests for new calculation functions:

1. Create a new test file following the naming pattern: `test-function-name.js`
2. Include mock implementations of the function and its dependencies
3. Create realistic test data matching the database schema
4. Add comprehensive test cases covering normal and edge scenarios
5. Update this README and the test runner

### Test Template
```javascript
/**
 * Test suite for [function name]
 * Run with: node src/tests/test-[function-name].js
 */

// Mock functions
function functionToTest(params) {
  // Implementation
}

// Test data
const mockData = [];

// Test functions
function assertEqual(actual, expected, testName, tolerance = 0.01) {
  // Assertion logic
}

// Run tests
console.log("🧪 Testing [function name]\n");
// Test cases...
console.log("\n🏁 Test suite completed!");
```

## 🚨 Important Notes

- Tests use mock exchange rates for predictable results
- All monetary values are tested in EUR unless specified otherwise
- Date formats follow ISO 8601 standard (YYYY-MM-DD)
- Transaction fees are included in cost calculations where applicable
- Tests validate both positive and negative scenarios
