"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { usePortfolioComposition } from "@/hooks/use-portfolio-composition";
import { AvailableChartColors, getColorClassName } from "@/lib/chartUtils";
import { PortfolioCompositionItem } from "@/utils/db/dashboard-queries";
import { SupportedCurrency } from "./currency-selector";
import { Loader2 } from "lucide-react";
import { useState } from "react";
import { DonutChart } from "../ui/donut-chart";

interface PortfolioCompositionChartProps {
  selectedPortfolios: string[];
  displayCurrency: SupportedCurrency;
}

type ViewType =
  | "sector"
  | "industry"
  | "currency"
  | "country"
  | "assetType"
  | "positions";

const viewLabels: Record<ViewType, string> = {
  sector: "Sectoare",
  industry: "Industrii",
  currency: "Monede",
  country: "<PERSON>ări",
  assetType: "Active",
  positions: "Poziții",
};

const formatCurrency = (value: number, currency: SupportedCurrency): string => {
  return new Intl.NumberFormat("ro-RO", {
    style: "currency",
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value);
};

const formatPercentage = (value: number): string => {
  return `${value.toFixed(2)}%`;
};

export function PortfolioCompositionChart({
  selectedPortfolios,
  displayCurrency,
}: PortfolioCompositionChartProps) {
  const [activeView, setActiveView] = useState<ViewType>("sector");
  const {
    data: composition,
    isLoading,
    error,
  } = usePortfolioComposition(selectedPortfolios, displayCurrency);

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-xl">Alocări</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            <div className="flex items-center gap-2 text-muted-foreground">
              <Loader2 className="h-4 w-4 animate-spin" />
              Se încarcă datele...
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-xl">Alocări</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            <div className="text-center text-muted-foreground">
              <p>Eroare la încărcarea datelor</p>
              <p className="text-sm mt-1">Vă rugăm să încercați din nou</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!composition || composition.totalValue === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-xl">Alocări</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            <div className="text-center text-muted-foreground">
              <p>Nu există date disponibile</p>
              <p className="text-sm mt-1">Selectați portofolii cu tranzacții</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getCurrentData = (): PortfolioCompositionItem[] => {
    switch (activeView) {
      case "sector":
        return composition.sector;
      case "industry":
        return composition.industry;
      case "currency":
        return composition.currency;
      case "country":
        return composition.country;
      case "assetType":
        return composition.assetType;
      case "positions":
        return composition.positions;
      default:
        return [];
    }
  };

  const currentData = getCurrentData();

  const chartData = currentData.map((item) => ({
    name: item.name,
    value: item.value,
    percentage: item.percentage,
    ticker: item.ticker,
  }));

  const chartColors = currentData.map(
    (_, index) => AvailableChartColors[index % AvailableChartColors.length]
  );

  const valueFormatter = (value: number) =>
    formatCurrency(value, displayCurrency);

  return (
    <Card className="h-full">
      <CardHeader className="pb-4 px-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-xl">Alocări</CardTitle>
          <Badge variant="secondary" className="text-xs">
            {formatCurrency(composition.totalValue, displayCurrency)}
          </Badge>
        </div>

        <div className="flex flex-wrap gap-1 mt-4">
          {(Object.keys(viewLabels) as ViewType[]).map((view) => (
            <Button
              key={view}
              variant={activeView === view ? "default" : "outline"}
              size="sm"
              className="text-xs h-7"
              onClick={() => setActiveView(view)}
              disabled={view === "sector" && composition.sector.length === 0}
            >
              {viewLabels[view]}
            </Button>
          ))}
        </div>
      </CardHeader>

      <CardContent className="px-4 mt-[-42px]">
        {currentData.length === 0 ? (
          <div className="h-64 flex items-center justify-center">
            <div className="text-center text-muted-foreground">
              <p>
                Nu există date pentru {viewLabels[activeView].toLowerCase()}
              </p>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex justify-center">
              <DonutChart
                data={chartData}
                category="name"
                value="value"
                valueFormatter={valueFormatter}
                customTooltip={({ active, payload }) => (
                  <CustomTooltip
                    active={active}
                    payload={payload}
                    chartData={chartData}
                    displayCurrency={displayCurrency}
                  />
                )}
                colors={chartColors}
                className="h-48 w-48"
                showLabel={true}
                animationDuration={500}
                animationBegin={100}
              />
            </div>

            {/* Center Value Display */}
            {/* <div className="text-center -mt-32 relative z-10 pointer-events-none">
              <div className="bg-background/80 backdrop-blur-sm rounded-lg p-2 inline-block">
                <p className="text-xs text-muted-foreground">Total Net Worth</p>
                <p className="text-lg font-semibold text-primary">
                  {formatCurrency(composition.totalValue)}
                </p>
              </div>
            </div> */}

            {/* Legend */}
            <div className="mt-8">
              <div className="grid grid-cols-1 gap-2 max-h-32 overflow-y-auto pe-2">
                {chartData.map((item, index) => (
                  <div
                    key={item.name}
                    className="flex items-center justify-between text-sm"
                  >
                    <div className="flex items-center gap-2 min-w-0 flex-1">
                      <div
                        className={`w-3 h-3 rounded-full flex-shrink-0 ${getColorClassName(
                          chartColors[index],
                          "bg"
                        )}`}
                      />
                      <span className="truncate" title={`${item.name}`}>
                        {item.name}
                      </span>
                    </div>
                    <div className="flex items-center gap-2 flex-shrink-0">
                      <span className="font-medium">
                        {formatPercentage(item.percentage)}
                      </span>
                      <span className="text-muted-foreground text-xs">
                        {formatCurrency(item.value, displayCurrency)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

import { TooltipProps } from "../ui/donut-chart";

const CustomTooltip = ({
  active,
  payload,
  chartData,
  displayCurrency,
}: TooltipProps & {
  chartData: PortfolioCompositionItem[];
  displayCurrency: SupportedCurrency;
}) => {
  if (active && payload && payload.length) {
    return (
      <div className="rounded-md border text-sm shadow-md bg-white dark:bg-gray-950 border-gray-200 dark:border-gray-800 p-2">
        {payload.map(({ value, category, color }, index) => {
          const item = chartData.find((d) => d.name === category);
          return (
            <div
              key={index}
              className="flex items-center justify-between space-x-8"
            >
              <div className="flex items-center space-x-2">
                <span
                  className={`size-2 shrink-0 rounded-full ${getColorClassName(
                    color,
                    "bg"
                  )}`}
                />
                <p className="text-gray-700 dark:text-gray-300 whitespace-nowrap">
                  {category}
                </p>
              </div>
              <div className="text-right">
                <p className="font-medium text-gray-900 dark:text-gray-50 tabular-nums">
                  {formatCurrency(value, displayCurrency)}
                </p>
                <p className="text-xs text-muted-foreground">
                  {formatPercentage(item?.percentage ?? 0)}
                </p>
              </div>
            </div>
          );
        })}
      </div>
    );
  }

  return null;
};
