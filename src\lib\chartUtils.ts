// Tremor Raw chartColors [v0.1.0]
export type ColorUtility = "bg" | "stroke" | "fill" | "text";

export const chartColors = {
  // === PORTAVIO BRAND COLORS (Priority) ===
  "portavio-orange": {
    bg: "bg-portavio-orange",
    stroke: "stroke-portavio-orange",
    fill: "fill-portavio-orange",
    text: "text-portavio-orange",
  },
  "portavio-blue": {
    bg: "bg-portavio-blue",
    stroke: "stroke-portavio-blue",
    fill: "fill-portavio-blue",
    text: "text-portavio-blue",
  },
  "portavio-blue-light": {
    bg: "bg-portavio-blue-light",
    stroke: "stroke-portavio-blue-light",
    fill: "fill-portavio-blue-light",
    text: "text-portavio-blue-light",
  },
  "portavio-blue-dark": {
    bg: "bg-portavio-blue-dark",
    stroke: "stroke-portavio-blue-dark",
    fill: "fill-portavio-blue-dark",
    text: "text-portavio-blue-dark",
  },
  "portavio-navy": {
    bg: "bg-portavio-navy",
    stroke: "stroke-portavio-navy",
    fill: "fill-portavio-navy",
    text: "text-portavio-navy",
  },

  // === STATE COLORS (Semantic) ===
  success: {
    bg: "bg-success",
    stroke: "stroke-success",
    fill: "fill-success",
    text: "text-success",
  },
  warning: {
    bg: "bg-warning",
    stroke: "stroke-warning",
    fill: "fill-warning",
    text: "text-warning",
  },
  error: {
    bg: "bg-error",
    stroke: "stroke-error",
    fill: "fill-error",
    text: "text-error",
  },
  info: {
    bg: "bg-info",
    stroke: "stroke-info",
    fill: "fill-info",
    text: "text-info",
  },

  // === COMPLEMENTARY COLORS (Diversity) ===
  emerald: {
    bg: "bg-emerald-500",
    stroke: "stroke-emerald-500",
    fill: "fill-emerald-500",
    text: "text-emerald-500",
  },
  violet: {
    bg: "bg-violet-500",
    stroke: "stroke-violet-500",
    fill: "fill-violet-500",
    text: "text-violet-500",
  },
  amber: {
    bg: "bg-amber-500",
    stroke: "stroke-amber-500",
    fill: "fill-amber-500",
    text: "text-amber-500",
  },
  cyan: {
    bg: "bg-cyan-500",
    stroke: "stroke-cyan-500",
    fill: "fill-cyan-500",
    text: "text-cyan-500",
  },
  pink: {
    bg: "bg-pink-500",
    stroke: "stroke-pink-500",
    fill: "fill-pink-500",
    text: "text-pink-500",
  },
  lime: {
    bg: "bg-lime-500",
    stroke: "stroke-lime-500",
    fill: "fill-lime-500",
    text: "text-lime-500",
  },
  fuchsia: {
    bg: "bg-fuchsia-500",
    stroke: "stroke-fuchsia-500",
    fill: "fill-fuchsia-500",
    text: "text-fuchsia-500",
  },
  gray: {
    bg: "bg-gray-500",
    stroke: "stroke-gray-500",
    fill: "fill-gray-500",
    text: "text-gray-500",
  },
} as const satisfies {
  [color: string]: {
    [key in ColorUtility]: string;
  };
};

export type AvailableChartColorsKeys = keyof typeof chartColors;

// Optimized color order for data visualization
// Prioritizes Portavio brand colors, then high-contrast complementary colors
export const AvailableChartColors: AvailableChartColorsKeys[] = [
  // Primary brand colors (most prominent)
  "portavio-orange",
  "portavio-blue",
  "violet",
  "pink",
  "emerald",
  "lime",
  "fuchsia",
  "amber",
  "cyan",
  "warning",
  "success",
  "portavio-blue-dark",

  // Semantic state colors (good contrast)
  "error",
  "portavio-blue-light",
  "info",

  // High-contrast complementary colors

  // Navy and gray (lower contrast, used last)
  "portavio-navy",
  "gray",
];

export const constructCategoryColors = (
  categories: string[],
  colors: AvailableChartColorsKeys[]
): Map<string, AvailableChartColorsKeys> => {
  const categoryColors = new Map<string, AvailableChartColorsKeys>();
  categories.forEach((category, index) => {
    categoryColors.set(category, colors[index % colors.length]);
  });
  return categoryColors;
};

export const getColorClassName = (
  color: AvailableChartColorsKeys,
  type: ColorUtility
): string => {
  const fallbackColor = {
    bg: "bg-gray-500",
    stroke: "stroke-gray-500",
    fill: "fill-gray-500",
    text: "text-gray-500",
  };
  return chartColors[color]?.[type] ?? fallbackColor[type];
};

// Tremor Raw getYAxisDomain [v0.0.0]
export const getYAxisDomain = (
  autoMinValue: boolean,
  minValue: number | undefined,
  maxValue: number | undefined
) => {
  const minDomain = autoMinValue ? "auto" : minValue ?? 0;
  const maxDomain = maxValue ?? "auto";
  return [minDomain, maxDomain];
};

// Tremor Raw hasOnlyOneValueForKey [v0.1.0]
export function hasOnlyOneValueForKey(
  array: any[],
  keyToCheck: string
): boolean {
  const val: any[] = [];

  for (const obj of array) {
    if (Object.prototype.hasOwnProperty.call(obj, keyToCheck)) {
      val.push(obj[keyToCheck]);
      if (val.length > 1) {
        return false;
      }
    }
  }

  return true;
}
