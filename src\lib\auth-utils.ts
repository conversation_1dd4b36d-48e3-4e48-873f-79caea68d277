import { toast } from "sonner";
import { authClient } from "./auth-client";
import { SignUpFormData, SignInFormData } from "./auth-schemas";
import {
  PasswordResetRequestFormData,
  PasswordResetFormData,
} from "./account-schemas";

export const authUtils = {
  // Sign up with email and username
  async signUp(data: Omit<SignUpFormData, "confirmPassword" | "acceptTerms">) {
    try {
      const result = await authClient.signUp.email({
        email: data.email,
        password: data.password,
        username: data.username,
        name: data.name,
        agreedTerms: true,
        callbackURL: "/auth/signin",
      });

      if (result.error) {
        throw new Error(result.error.message || "Eroare la înregistrare");
      }

      return { success: true, data: result.data };
    } catch (error) {
      console.error("Sign up error:", error);
      throw error;
    }
  },

  // Sign in with email or username
  async signIn(data: SignInFormData) {
    try {
      let result: any;

      // If identifier looks like email, try email sign-in first
      if (data.identifier.includes("@")) {
        result = await authClient.signIn.email(
          {
            email: data.identifier,
            password: data.password,
          },
          {
            onError: (ctx) => {
              if (ctx.error.status === 403) {
                toast.info("Vă rugăm să verificați adresa de email.", {
                  duration: 10000,
                });
              } else {
                toast.error(
                  "S-a produs o eroare la autentificare. Verificați datele introduse sau contactați suportul tehnic."
                );
              }
            },
          }
        );

        // If email sign-in fails, try username sign-in
        if (result.error) {
          result = await authClient.signIn.username(
            {
              username: data.identifier,
              password: data.password,
            },
            {
              onError: (ctx) => {
                if (ctx.error.status === 403) {
                  toast.info("Vă rugăm să verificați adresa de email.", {
                    duration: 10000,
                  });
                }
              },
            }
          );
        }
      } else {
        result = await authClient.signIn.username(
          {
            username: data.identifier,
            password: data.password,
          },
          {
            onError: (ctx) => {
              if (ctx.error.status === 403) {
                toast.info("Vă rugăm să verificați adresa de email.", {
                  duration: 10000,
                });
              } else {
                toast.error(
                  "S-a produs o eroare la autentificare. Verificați datele introduse sau contactați suportul tehnic."
                );
              }
            },
          }
        );
      }

      if (result.error) {
        throw new Error(
          "S-a produs o eroare la autentificare. Verificați datele introduse sau contactați suportul tehnic."
        );
      }

      return { success: true, data: result.data };
    } catch (error) {
      console.error("Sign in error:", error);
      throw error;
    }
  },

  async signInWithGoogle() {
    try {
      // Add context to the callback URL to track sending welcome mail from both signup and signin
      const callbackURL = "/profile?oauth_context=signup";

      const result = await authClient.signIn.social({
        provider: "google",
        callbackURL,
      });

      if (result.error) {
        throw new Error(
          result.error.message || "Eroare la autentificare cu Google"
        );
      }

      return { success: true, data: result.data };
    } catch (error) {
      console.error("Google sign in error:", error);
      throw error;
    }
  },

  async signOut(callback?: () => void) {
    try {
      const result = await authClient.signOut({
        fetchOptions: {
          onSuccess: () => {
            if (callback) {
              callback();
            }
          },
        },
      });
      if (result.error) {
        throw new Error(result.error.message || "Eroare la deconectare");
      }
      return { success: true };
    } catch (error) {
      console.error("Sign out error:", error);
      throw error;
    }
  },

  async getSession() {
    try {
      const session = await authClient.getSession();
      return session;
    } catch (error) {
      console.error("Get session error:", error);
      return null;
    }
  },

  async updateUser(data: { username?: string; name?: string }) {
    try {
      const result = await authClient.updateUser(data);
      if (result.error) {
        throw new Error(result.error.message || "Eroare la actualizare profil");
      }
      return { success: true, data: result.data };
    } catch (error) {
      console.error("Update user error:", error);
      throw error;
    }
  },

  async requestPasswordReset(data: PasswordResetRequestFormData) {
    try {
      const result = await authClient.requestPasswordReset({
        email: data.email,
        redirectTo: "/auth/reset-password",
      });

      if (result.error) {
        // Check for specific error messages that might indicate OAuth account
        const errorMessage =
          result.error.message || "Eroare la solicitarea resetării parolei";

        // For security, we don't reveal specific account types in error messages
        // but we can provide a generic message that covers both cases
        if (
          errorMessage.includes("User not found") ||
          errorMessage.includes("Invalid email")
        ) {
          throw new Error(
            "Dacă adresa de email există în sistem și are parolă setată, veți primi instrucțiunile de resetare."
          );
        }

        throw new Error(errorMessage);
      }

      return { success: true, data: result.data };
    } catch (error) {
      console.error("Request password reset error:", error);
      throw error;
    }
  },

  async resetPassword(data: PasswordResetFormData) {
    try {
      const result = await authClient.resetPassword({
        newPassword: data.newPassword,
        token: data.token,
      });

      if (result.error) {
        throw new Error(result.error.message || "Eroare la resetarea parolei");
      }

      return { success: true, data: result.data };
    } catch (error) {
      console.error("Reset password error:", error);
      throw error;
    }
  },
};
