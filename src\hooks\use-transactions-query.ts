"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { TransactionWithAssetInfo } from "@/utils/db/portfolio-queries";

// Types for API responses
interface TransactionsResponse {
  transactions: TransactionWithAssetInfo[];
  count: number;
  totalCount: number;
  portfolio: {
    id: string;
    name: string;
    description?: string;
  };
  message: string;
}

interface TransactionsQueryParams {
  portfolioId: string;
}

// Query keys
export const transactionsKeys = {
  all: ["transactions"] as const,
  lists: () => [...transactionsKeys.all, "list"] as const,
  list: (params: TransactionsQueryParams) =>
    [...transactionsKeys.lists(), params] as const,
  details: () => [...transactionsKeys.all, "detail"] as const,
  detail: (id: string) => [...transactionsKeys.details(), id] as const,
};

// Fetch transactions function
async function fetchTransactions(
  params: TransactionsQueryParams
): Promise<TransactionsResponse> {
  const { portfolioId } = params;

  const response = await fetch(`/api/portfolios/${portfolioId}/transactions`);

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || "Nu s-au putut încărca tranzacțiile");
  }

  return response.json();
}

// Delete transaction function
async function deleteTransaction(transactionId: string): Promise<void> {
  const response = await fetch(`/api/transactions/${transactionId}`, {
    method: "DELETE",
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || "Nu s-a putut șterge tranzacția");
  }
}

// Update transaction function
async function updateTransaction(
  transactionId: string,
  data: any
): Promise<TransactionWithAssetInfo> {
  const response = await fetch(`/api/transactions/${transactionId}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || "Nu s-a putut actualiza tranzacția");
  }

  const result = await response.json();
  return result.transaction;
}

// Hook for fetching transactions
export function useTransactions(params: TransactionsQueryParams) {
  return useQuery({
    queryKey: transactionsKeys.list(params),
    queryFn: () => fetchTransactions(params),
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 5 * 60 * 1000, // 5 minutes (formerly cacheTime)
  });
}

// Hook for deleting a transaction
export function useDeleteTransaction() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ["deleteTransaction"],
    mutationFn: deleteTransaction,
    onSuccess: () => {
      // Invalidate and refetch transactions queries
      queryClient.invalidateQueries({
        queryKey: transactionsKeys.lists(),
      });
    },
  });
}

export function useUpdateTransaction() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ["updateTransaction"],
    mutationFn: ({
      transactionId,
      data,
    }: {
      transactionId: string;
      data: any;
    }) => updateTransaction(transactionId, data),
    onSuccess: () => {
      // Invalidate and refetch transactions queries
      queryClient.invalidateQueries({
        queryKey: transactionsKeys.lists(),
      });
    },
  });
}

// Hook for creating a transaction
export function useCreateTransaction() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ["createTransaction"],
    mutationFn: async (data: any) => {
      const response = await fetch("/api/transactions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Nu s-a putut crea tranzacția");
      }

      const result = await response.json();
      return result.transaction;
    },
    onSuccess: () => {
      // Invalidate and refetch transactions queries
      queryClient.invalidateQueries({
        queryKey: transactionsKeys.lists(),
      });
    },
  });
}
