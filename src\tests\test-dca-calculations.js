/**
 * Test DCA (Dollar Cost Average) calculations
 * This test verifies the DCA price and percentage calculations
 */

// Mock transaction data for testing
const mockTransactions = [
  {
    id: "1",
    ticker: "AAPL",
    price: 100,
    quantity: 1,
    transaction_type: "BUY",
    transaction_date: "2024-01-01",
    transaction_fee: 5
  },
  {
    id: "2", 
    ticker: "AAPL",
    price: 150,
    quantity: 2,
    transaction_type: "BUY",
    transaction_date: "2024-02-01",
    transaction_fee: 7.5
  },
  {
    id: "3",
    ticker: "AAPL", 
    price: 120,
    quantity: 1,
    transaction_type: "BUY",
    transaction_date: "2024-03-01",
    transaction_fee: 3
  }
];

// DCA calculation functions (copied from dashboard-queries.ts)
function calculateDCAPrice(buyTransactions) {
  if (buyTransactions.length === 0) return 0;

  let totalWeightedCost = 0; // Σ(Qi × Pi)
  let totalQuantity = 0; // ΣQi

  buyTransactions.forEach((transaction) => {
    totalWeightedCost += transaction.quantity * transaction.price;
    totalQuantity += transaction.quantity;
  });

  return totalQuantity > 0 ? totalWeightedCost / totalQuantity : 0;
}

function calculateDCAPercentage(currentPrice, dcaPrice) {
  if (dcaPrice <= 0) return 0;
  return ((currentPrice - dcaPrice) / dcaPrice) * 100;
}

function calculateTickerTransactionFees(transactions) {
  let totalFees = 0;
  transactions.forEach((transaction) => {
    if (transaction.transaction_fee && transaction.transaction_fee > 0) {
      totalFees += transaction.transaction_fee;
    }
  });
  return totalFees;
}

// Test DCA calculations
function testDCACalculations() {
  console.log("🧮 Testing DCA Calculations");
  console.log("=" .repeat(50));

  // Test data:
  // Transaction 1: 1 unit @ $100
  // Transaction 2: 2 units @ $150  
  // Transaction 3: 1 unit @ $120
  // Expected DCA = (1×100 + 2×150 + 1×120) / (1+2+1) = 520/4 = $130

  const dcaPrice = calculateDCAPrice(mockTransactions);
  console.log(`📊 DCA Price Calculation:`);
  console.log(`   Transaction 1: 1 × $100 = $100`);
  console.log(`   Transaction 2: 2 × $150 = $300`);
  console.log(`   Transaction 3: 1 × $120 = $120`);
  console.log(`   Total Cost: $520`);
  console.log(`   Total Quantity: 4`);
  console.log(`   DCA Price: $520 / 4 = $${dcaPrice}`);
  console.log(`   Expected: $130`);
  console.log(`   ✅ ${dcaPrice === 130 ? 'PASS' : 'FAIL'}`);

  // Test DCA percentage with different current prices
  const testPrices = [
    { current: 180, expected: 38.46 }, // (180-130)/130 * 100 = 38.46%
    { current: 100, expected: -23.08 }, // (100-130)/130 * 100 = -23.08%
    { current: 130, expected: 0 } // (130-130)/130 * 100 = 0%
  ];

  console.log(`\n📈 DCA Percentage Calculations:`);
  testPrices.forEach(({ current, expected }) => {
    const dcaPercentage = calculateDCAPercentage(current, dcaPrice);
    const rounded = Math.round(dcaPercentage * 100) / 100;
    console.log(`   Current Price: $${current}`);
    console.log(`   DCA Percentage: ${rounded}%`);
    console.log(`   Expected: ${expected}%`);
    console.log(`   ✅ ${Math.abs(rounded - expected) < 0.01 ? 'PASS' : 'FAIL'}`);
    console.log('');
  });

  // Test transaction fees calculation
  const totalFees = calculateTickerTransactionFees(mockTransactions);
  const expectedFees = 5 + 7.5 + 3; // 15.5
  console.log(`💰 Transaction Fees Calculation:`);
  console.log(`   Fee 1: $5`);
  console.log(`   Fee 2: $7.5`);
  console.log(`   Fee 3: $3`);
  console.log(`   Total Fees: $${totalFees}`);
  console.log(`   Expected: $${expectedFees}`);
  console.log(`   ✅ ${totalFees === expectedFees ? 'PASS' : 'FAIL'}`);

  console.log("\n" + "=" .repeat(50));
  console.log("🎯 DCA Calculations Test Complete");
}

// Run the test
testDCACalculations();
