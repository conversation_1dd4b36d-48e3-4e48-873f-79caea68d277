"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  AccountDetailsFormDataFlexible,
  createAccountDetailsSchema,
  AccountCapabilities,
} from "@/lib/account-schemas";
import { zodResolver } from "@hookform/resolvers/zod";
import { AlertCircle, Save, CheckCircle } from "lucide-react";
import { useForm } from "react-hook-form";

interface AccountDetailsFormProps {
  initialData: AccountDetailsFormDataFlexible & {
    createdAt?: string;
    emailVerified?: boolean;
  };
  capabilities: AccountCapabilities;
  onSubmit: (data: AccountDetailsFormDataFlexible) => Promise<unknown>;
  isLoading?: boolean;
  error?: string;
}

export function AccountDetailsForm({
  initialData,
  capabilities,
  onSubmit,
  isLoading = false,
  error,
}: AccountDetailsFormProps) {
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isDirty },
  } = useForm<AccountDetailsFormDataFlexible>({
    resolver: zodResolver(
      createAccountDetailsSchema(capabilities, initialData.username)
    ),
    defaultValues: initialData,
  });

  const handleFormSubmit = async (data: AccountDetailsFormDataFlexible) => {
    try {
      const errorReturned = await onSubmit(data);
      if (!errorReturned) {
        reset(data);
      }
    } catch (error) {
      console.error("Error updating account details:", error);
    }
  };

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      {error && (
        <div className="rounded-md bg-red-50 p-3 text-sm text-red-600 dark:bg-red-900/20 dark:text-red-400">
          {error}
        </div>
      )}

      <div className="space-y-2">
        <Label htmlFor="email">Adresă de email</Label>
        <div className="relative">
          <Input
            id="email"
            type="email"
            {...register("email")}
            disabled={!capabilities.canChangeEmail || isLoading || true}
            className={`${errors.email ? "border-red-500" : ""} pr-10`}
          />
          <div className="absolute inset-y-0 right-0 flex items-center pr-3">
            {initialData.emailVerified ? (
              <div className="flex items-center gap-1" title="Email verificat">
                <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
                <span className="text-xs text-green-600 dark:text-green-400 hidden sm:inline">
                  Verificat
                </span>
              </div>
            ) : (
              <div
                className="flex items-center gap-1"
                title="Email neverificat"
              >
                <AlertCircle className="h-4 w-4 text-amber-500 dark:text-amber-400" />
                <span className="text-xs text-amber-600 dark:text-amber-400 hidden sm:inline">
                  Neverificat
                </span>
              </div>
            )}
          </div>
        </div>
        <p className="text-xs text-muted-foreground">
          Adresa de email nu poate fi modificată
        </p>

        {errors.email && (
          <p className="text-sm text-red-500 flex items-center gap-1">
            <AlertCircle className="h-4 w-4" />
            {errors.email.message}
          </p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="name">Nume complet</Label>
        <Input
          id="name"
          type="text"
          {...register("name")}
          disabled={!capabilities.canChangeName || isLoading}
          className={errors.name ? "border-red-500" : ""}
        />
        {errors.name && (
          <p className="text-sm text-red-500 flex items-center gap-1">
            <AlertCircle className="h-4 w-4" />
            {errors.name.message}
          </p>
        )}
      </div>

      {capabilities.canChangeUsername && (
        <div className="space-y-2">
          <Label htmlFor="username">
            Username{" "}
            <span className="text-xs italic">
              {capabilities.isGoogleAccount
                ? "(unic, folosit pentru afișare)"
                : "(unic, folosit pentru autentificare)"}
            </span>
          </Label>
          <Input
            id="username"
            type="text"
            {...register("username")}
            disabled={!capabilities.canChangeUsername || isLoading}
            className={errors.username ? "border-red-500" : ""}
            placeholder={
              capabilities.isGoogleAccount && !initialData.username
                ? "Introduceți un username (opțional)"
                : ""
            }
          />

          {errors.username && (
            <p className="text-sm text-red-500 flex items-center gap-1">
              <AlertCircle className="h-4 w-4" />
              {errors.username.message}
            </p>
          )}
        </div>
      )}

      <div className="space-y-2">
        <Label>Data creării contului</Label>
        <Input
          value={new Date(initialData.createdAt || "").toLocaleString("ro-RO")}
          disabled
          className="bg-muted"
        />
        <p className="text-xs text-muted-foreground">
          Această informație nu poate fi modificată
        </p>
      </div>

      <div className="flex justify-end">
        <Button
          type="submit"
          disabled={!isDirty || isLoading}
          className="min-w-[120px]"
        >
          {isLoading ? (
            <div className="flex items-center gap-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              Se salvează...
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <Save className="h-4 w-4" />
              Salvează
            </div>
          )}
        </Button>
      </div>
    </form>
  );
}
