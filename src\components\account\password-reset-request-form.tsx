"use client";

import { But<PERSON> } from "@/components/ui/button";
import { authUtils } from "@/lib/auth-utils";
import { AlertCircle, CheckCircle, Mail } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

interface PasswordResetRequestFormProps {
  userEmail: string;
  canChangePassword: boolean;
}

export function PasswordResetRequestForm({
  userEmail,
  canChangePassword,
}: PasswordResetRequestFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const handleRequestReset = async () => {
    setIsLoading(true);
    try {
      await authUtils.requestPasswordReset({ email: userEmail });
      setIsSuccess(true);
      toast.success("Email-ul de resetare a fost trimis cu succes!");
    } catch (error) {
      console.error("Password reset request error:", error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : "A apărut o eroare la solicitarea resetării parolei";
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  if (!canChangePassword) {
    return (
      <div className="space-y-4">
        <div className="flex items-center gap-4 p-4 bg-muted rounded-lg">
          <AlertCircle className="h-5 w-5 text-muted-foreground" />
          <div>
            <p className="font-medium">
              Schimbarea parolei nu este disponibilă
            </p>
            <p className="text-sm text-muted-foreground">
              Contul tău folosește autentificare externă (Google). Pentru a
              schimba parola, accesează setările contului tău Google.
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (isSuccess) {
    return (
      <div className="space-y-4">
        <div className="flex items-center gap-2 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
          <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
          <div>
            <p className="font-medium text-green-800 dark:text-green-200">
              Email trimis cu succes!
            </p>
            <p className="text-sm text-green-700 dark:text-green-300">
              Am trimis instrucțiunile de resetare a parolei la adresa{" "}
              <span className="font-medium">{userEmail}</span>
            </p>
          </div>
        </div>

        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <AlertCircle className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5" />
            <div className="text-sm text-blue-800 dark:text-blue-200">
              <p className="font-medium mb-1">Verifică-ți inbox-ul</p>
              <ul className="space-y-1 text-blue-700 dark:text-blue-300">
                <li>• Link-ul este valabil doar 1 oră</li>
                <li>• Verifică și folderul de spam</li>
                <li>• Poți solicita un nou email dacă este necesar</li>
              </ul>
            </div>
          </div>
        </div>

        <Button
          onClick={() => setIsSuccess(false)}
          variant="outline"
          className="w-full"
        >
          Trimite din nou
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6 lg:w-[66%] w-full">
      <div className="space-y-4">
        <div className="flex items-center gap-4 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
          <AlertCircle className="h-5 w-5 text-blue-600 dark:text-blue-400" />
          <div>
            <p className="font-medium text-blue-800 dark:text-blue-200">
              Schimbare parolă prin email
            </p>
            <p className="text-sm text-blue-700 dark:text-blue-300">
              Pentru securitate, îți vom trimite un link de resetare a parolei
              la adresa ta de email.
            </p>
          </div>
        </div>

        <div className="space-y-2">
          <p className="text-sm text-muted-foreground">
            Email-ul va fi trimis la:
          </p>
          <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
            <Mail className="h-4 w-4 text-muted-foreground" />
            <span className="font-medium">{userEmail}</span>
          </div>
        </div>
      </div>

      <div className="flex justify-end">
        <Button
          onClick={handleRequestReset}
          disabled={isLoading}
          className="min-w-[140px]"
        >
          {isLoading ? (
            <div className="flex items-center gap-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              Se trimite...
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <Mail className="h-4 w-4" />
              Trimite email resetare
            </div>
          )}
        </Button>
      </div>
    </div>
  );
}
