import { hasuraQuery, hasuraMutation } from "./hasura";
import { sendWelcomeEmail } from "@/lib/email-verification";

// GraphQL Queries

/**
 * Check if welcome email has been sent to a user
 */
export const CHECK_WELCOME_EMAIL_SENT = `
  query CheckWelcomeEmailSent($userId: String!) {
    ptvuser_user_by_pk(id: $userId) {
      id
      email
      name
      welcomeEmailSent
    }
  }
`;

/**
 * Check welcome email status by email address
 */
export const CHECK_WELCOME_EMAIL_SENT_BY_EMAIL = `
  query CheckWelcomeEmailSentByEmail($email: String!) {
    ptvuser_user(where: {email: {_eq: $email}}, limit: 1) {
      id
      email
      name
      welcomeEmailSent
    }
  }
`;

// GraphQL Mutations

/**
 * Update welcome email sent status for a user
 */
export const UPDATE_WELCOME_EMAIL_SENT = `
  mutation UpdateWelcomeEmailSent($userId: String!, $welcomeEmailSent: Boolean!) {
    update_ptvuser_user_by_pk(
      pk_columns: {id: $userId}, 
      _set: {welcomeEmailSent: $welcomeEmailSent}
    ) {
      id
      email
      name
      welcomeEmailSent
      updatedAt
    }
  }
`;

// Utility Functions

export interface WelcomeEmailStatus {
  id: string;
  email: string;
  name?: string;
  welcomeEmailSent: boolean;
}

export interface WelcomeEmailResult {
  success: boolean;
  message: string;
  emailSent: boolean;
  alreadySent?: boolean;
  error?: string;
}

/**
 * Check if welcome email has been sent to a user by user ID
 */
export async function checkWelcomeEmailSent(userId: string): Promise<WelcomeEmailStatus | null> {
  try {
    const result = await hasuraQuery<{
      ptvuser_user_by_pk: WelcomeEmailStatus | null;
    }>(CHECK_WELCOME_EMAIL_SENT, { variables: { userId } });

    return result.ptvuser_user_by_pk;
  } catch (error) {
    console.error("Error checking welcome email status:", error);
    throw new Error("Nu s-a putut verifica statusul email-ului de bun venit");
  }
}

/**
 * Check if welcome email has been sent to a user by email address
 */
export async function checkWelcomeEmailSentByEmail(email: string): Promise<WelcomeEmailStatus | null> {
  try {
    const result = await hasuraQuery<{
      ptvuser_user: WelcomeEmailStatus[];
    }>(CHECK_WELCOME_EMAIL_SENT_BY_EMAIL, { variables: { email } });

    return result.ptvuser_user?.[0] || null;
  } catch (error) {
    console.error("Error checking welcome email status by email:", error);
    throw new Error("Nu s-a putut verifica statusul email-ului de bun venit");
  }
}

/**
 * Update welcome email sent status for a user
 */
export async function updateWelcomeEmailSent(
  userId: string, 
  welcomeEmailSent: boolean
): Promise<WelcomeEmailStatus> {
  try {
    const result = await hasuraMutation<{
      update_ptvuser_user_by_pk: WelcomeEmailStatus;
    }>(UPDATE_WELCOME_EMAIL_SENT, {
      variables: {
        userId,
        welcomeEmailSent,
      },
    });

    if (!result.update_ptvuser_user_by_pk) {
      throw new Error("Nu s-a putut actualiza statusul email-ului de bun venit");
    }

    return result.update_ptvuser_user_by_pk;
  } catch (error) {
    console.error("Error updating welcome email status:", error);
    throw new Error("Nu s-a putut actualiza statusul email-ului de bun venit");
  }
}

/**
 * Send welcome email if not already sent
 * This is the main function that handles the complete welcome email logic
 */
export async function sendWelcomeEmailIfNeeded(
  userId: string,
  userEmail?: string,
  userName?: string
): Promise<WelcomeEmailResult> {
  try {
    // Check if welcome email has already been sent
    const userStatus = await checkWelcomeEmailSent(userId);
    
    if (!userStatus) {
      return {
        success: false,
        message: "Utilizatorul nu a fost găsit",
        emailSent: false,
        error: "USER_NOT_FOUND",
      };
    }

    // If welcome email was already sent, skip
    if (userStatus.welcomeEmailSent) {
      return {
        success: true,
        message: "Email-ul de bun venit a fost deja trimis",
        emailSent: false,
        alreadySent: true,
      };
    }

    // Send welcome email
    const emailToUse = userEmail || userStatus.email;
    const nameToUse = userName || userStatus.name;

    await sendWelcomeEmail(emailToUse, nameToUse);

    // Update database to mark welcome email as sent
    await updateWelcomeEmailSent(userId, true);

    console.log(`Welcome email sent successfully to user: ${emailToUse}`);

    return {
      success: true,
      message: "Email-ul de bun venit a fost trimis cu succes",
      emailSent: true,
      alreadySent: false,
    };
  } catch (error) {
    console.error("Error in sendWelcomeEmailIfNeeded:", error);
    
    return {
      success: false,
      message: "Eroare la trimiterea email-ului de bun venit",
      emailSent: false,
      error: error instanceof Error ? error.message : "UNKNOWN_ERROR",
    };
  }
}
