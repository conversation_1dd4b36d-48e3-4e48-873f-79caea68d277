import { accountDetailsUpdateSchema } from "@/lib/account-schemas";
import { auth } from "@/lib/auth";
import { APIError } from "better-auth/api";
import { headers } from "next/headers";
import { NextRequest, NextResponse } from "next/server";

export async function PUT(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.user) {
      return NextResponse.json(
        { error: "Nu ești autentificat" },
        { status: 401 }
      );
    }

    const body = await request.json();

    const validation = accountDetailsUpdateSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        {
          error: "Datele introduse nu sunt valide",
          details: validation.error.errors,
        },
        { status: 400 }
      );
    }

    const updateData = validation.data;

    try {
      await auth.api.updateUser({
        headers: await headers(),
        body: updateData,
      });
    } catch (authError) {
      console.error("Error updating user in better-auth:", authError);
      if (authError instanceof APIError) {
        return NextResponse.json(
          {
            error:
              authError.statusCode === 422
                ? "Acest username este deja folosit. Te rugăm să alegi un alt username."
                : authError.message,
          },
          { status: authError.statusCode }
        );
      }
    }

    return NextResponse.json({
      success: true,
      message: "Profilul a fost actualizat cu succes",
    });
  } catch (error) {
    console.error("Error updating profile:", error);

    if (error instanceof Error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(
      { error: "A apărut o eroare la actualizarea profilului" },
      { status: 500 }
    );
  }
}
