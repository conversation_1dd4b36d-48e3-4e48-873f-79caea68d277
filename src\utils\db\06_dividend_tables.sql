-- Dividend and Financial Data Tables
-- Tables for storing dividend payments and related financial data

-- Create dividend table
CREATE TABLE IF NOT EXISTS ptvuser.dividend (
    dividend_id SERIAL PRIMARY KEY,
    asset_id INTEGER NOT NULL REFERENCES ptvuser.asset(asset_id) ON DELETE CASCADE,
    ex_date DATE NOT NULL,
    amount_per_share NUMERIC(20, 8) NOT NULL,
    dividend_type VARCHAR(50) NOT NULL DEFAULT 'Regular',
    status VARCHAR(20) NOT NULL DEFAULT 'Announced',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Unique constraint to prevent duplicate dividends for same asset and ex_date
    CONSTRAINT dividend_asset_ex_date_unique UNIQUE (asset_id, ex_date),
    -- Validate amount is positive
    CONSTRAINT dividend_amount_positive CHECK (amount_per_share > 0),
    -- Validate dividend type
    CONSTRAINT dividend_type_check CHECK (dividend_type IN ('Regular', 'Special', 'Interim', 'Final')),
    -- Validate status
    CONSTRAINT dividend_status_check CHECK (status IN ('Announced', 'Confirmed', 'Paid', 'Cancelled'))
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_dividend_asset_id ON ptvuser.dividend(asset_id);
CREATE INDEX IF NOT EXISTS idx_dividend_ex_date ON ptvuser.dividend(ex_date DESC);
CREATE INDEX IF NOT EXISTS idx_dividend_asset_ex_date ON ptvuser.dividend(asset_id, ex_date DESC);
CREATE INDEX IF NOT EXISTS idx_dividend_status ON ptvuser.dividend(status);
CREATE INDEX IF NOT EXISTS idx_dividend_type ON ptvuser.dividend(dividend_type);

-- Create updated_at trigger
CREATE TRIGGER update_dividend_updated_at
    BEFORE UPDATE ON ptvuser.dividend
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE ptvuser.dividend IS 'Dividend payments for assets';
COMMENT ON COLUMN ptvuser.dividend.dividend_id IS 'Unique identifier for the dividend record';
COMMENT ON COLUMN ptvuser.dividend.asset_id IS 'Reference to the asset paying the dividend';
COMMENT ON COLUMN ptvuser.dividend.ex_date IS 'Ex-dividend date (date when stock goes ex-dividend)';
COMMENT ON COLUMN ptvuser.dividend.amount_per_share IS 'Dividend amount per share';
COMMENT ON COLUMN ptvuser.dividend.dividend_type IS 'Type of dividend (Regular, Special, Interim, Final)';
COMMENT ON COLUMN ptvuser.dividend.status IS 'Status of dividend (Announced, Confirmed, Paid, Cancelled)';
COMMENT ON COLUMN ptvuser.dividend.created_at IS 'Timestamp when the dividend record was created';
COMMENT ON COLUMN ptvuser.dividend.updated_at IS 'Timestamp when the dividend record was last updated';
