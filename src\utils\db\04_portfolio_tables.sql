-- Portfolio Management Tables
-- Tables for managing user portfolios and transactions

-- Create portfolios table
CREATE TABLE IF NOT EXISTS ptvuser.portfolios (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id TEXT NOT NULL REFERENCES "user"(id) ON DELETE CASCADE,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    description TEXT,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Validate name is not empty
    CONSTRAINT portfolios_name_check CHECK (LENGTH(TRIM(name)) > 0)
);

-- Create transactions table to store portfolio transactions
CREATE TABLE IF NOT EXISTS ptvuser.transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    portfolio_id UUID NOT NULL REFERENCES ptvuser.portfolios(id) ON DELETE CASCADE,
    ticker VARCHAR(20) NOT NULL,
    quantity NUMERIC(20, 8) NOT NULL,
    price NUMERIC(20, 8),
    transaction_date DATE NOT NULL,
    transaction_type VARCHAR(10) NOT NULL DEFAULT 'BUY',
    transaction_fee DECIMAL(15,8),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Validate transaction type
    CONSTRAINT transactions_type_check CHECK (transaction_type IN ('BUY', 'SELL')),
    -- Validate quantity is positive
    CONSTRAINT transactions_quantity_check CHECK (quantity > 0),
    -- Validate price is positive (if provided)
    CONSTRAINT transactions_price_check CHECK (price IS NULL OR price > 0),
    -- Validate ticker is not empty
    CONSTRAINT transactions_ticker_check CHECK (LENGTH(TRIM(ticker)) > 0),
    -- Validate transaction date is not in the future
    CONSTRAINT transactions_date_check CHECK (transaction_date <= CURRENT_DATE)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_portfolios_user_id ON ptvuser.portfolios(user_id);
CREATE INDEX IF NOT EXISTS idx_portfolios_is_active ON ptvuser.portfolios(is_active);
CREATE INDEX IF NOT EXISTS idx_portfolios_user_active ON ptvuser.portfolios(user_id, is_active);

CREATE INDEX IF NOT EXISTS idx_transactions_portfolio_id ON ptvuser.transactions(portfolio_id);
CREATE INDEX IF NOT EXISTS idx_transactions_ticker ON ptvuser.transactions(ticker);
CREATE INDEX IF NOT EXISTS idx_transactions_date ON ptvuser.transactions(transaction_date DESC);
CREATE INDEX IF NOT EXISTS idx_transactions_portfolio_date ON ptvuser.transactions(portfolio_id, transaction_date DESC);
CREATE INDEX IF NOT EXISTS idx_transactions_portfolio_ticker ON ptvuser.transactions(portfolio_id, ticker);
CREATE INDEX IF NOT EXISTS idx_transactions_type ON ptvuser.transactions(transaction_type);

-- Create updated_at triggers
CREATE TRIGGER update_portfolios_updated_at
    BEFORE UPDATE ON ptvuser.portfolios
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_transactions_updated_at
    BEFORE UPDATE ON ptvuser.transactions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE ptvuser.portfolios IS 'User investment portfolios';
COMMENT ON COLUMN ptvuser.portfolios.id IS 'Unique identifier for the portfolio';
COMMENT ON COLUMN ptvuser.portfolios.user_id IS 'Reference to the user who owns this portfolio';
COMMENT ON COLUMN ptvuser.portfolios.name IS 'Portfolio name (e.g., "My Portfolio", "Retirement Fund")';
COMMENT ON COLUMN ptvuser.portfolios.description IS 'Optional portfolio description';
COMMENT ON COLUMN ptvuser.portfolios.is_active IS 'Whether the portfolio is active (soft delete)';

COMMENT ON TABLE ptvuser.transactions IS 'Stores individual buy/sell transactions for portfolios';
COMMENT ON COLUMN ptvuser.transactions.id IS 'Unique identifier for the transaction';
COMMENT ON COLUMN ptvuser.transactions.portfolio_id IS 'Reference to the portfolio this transaction belongs to';
COMMENT ON COLUMN ptvuser.transactions.ticker IS 'Asset ticker symbol (e.g., AAPL, MSFT)';
COMMENT ON COLUMN ptvuser.transactions.quantity IS 'Number of shares/units bought or sold';
COMMENT ON COLUMN ptvuser.transactions.price IS 'Price per share/unit (optional for manual entry)';
COMMENT ON COLUMN ptvuser.transactions.transaction_date IS 'Date when the transaction occurred (critical for historical price lookups)';
COMMENT ON COLUMN ptvuser.transactions.transaction_type IS 'Type of transaction: BUY or SELL';
COMMENT ON COLUMN ptvuser.transactions.transaction_fee IS 'Optional transaction fee/commission amount (supports up to 8 decimal places)';
COMMENT ON COLUMN ptvuser.transactions.notes IS 'Optional user notes about the transaction';
COMMENT ON COLUMN ptvuser.transactions.created_at IS 'Timestamp when the transaction record was created';
COMMENT ON COLUMN ptvuser.transactions.updated_at IS 'Timestamp when the transaction record was last updated';
