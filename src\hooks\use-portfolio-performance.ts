"use client";

import { useQuery } from "@tanstack/react-query";
import {
  PortfolioPerformanceData,
  TimePeriod,
} from "@/utils/db/dashboard-queries";
import { SupportedCurrency } from "@/components/dashboard/currency-selector";

// Query keys for portfolio performance
export const portfolioPerformanceKeys = {
  all: ["portfolio-performance"] as const,
  lists: () => [...portfolioPerformanceKeys.all, "list"] as const,
  list: (
    portfolioIds: string[],
    timePeriod: TimePeriod,
    displayCurrency: SupportedCurrency
  ) =>
    [
      ...portfolioPerformanceKeys.lists(),
      { portfolioIds, timePeriod, displayCurrency },
    ] as const,
};

// API response interface
interface PortfolioPerformanceResponse {
  success: boolean;
  data: PortfolioPerformanceData;
  message: string;
}

// Fetch function for portfolio performance data
async function fetchPortfolioPerformance(
  portfolioIds: string[],
  timePeriod: TimePeriod,
  displayCurrency: SupportedCurrency = "EUR"
): Promise<PortfolioPerformanceData> {
  if (portfolioIds.length === 0) {
    return {
      data: [],
      currentValue: 0,
      basePortfolioValue: 0,
      totalProfitLoss: 0,
      totalProfitLossPercentage: 0,
      displayCurrency,
      timePeriod,
    };
  }

  // Use GET for smaller lists, POST for larger ones - more than 4 portfolios
  const usePost = portfolioIds.length > 4;

  let response: Response;

  if (usePost) {
    response = await fetch("/api/dashboard/portfolio-performance", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ portfolioIds, timePeriod, displayCurrency }),
    });
  } else {
    const params = new URLSearchParams({
      portfolioIds: portfolioIds.join(","),
      timePeriod,
      displayCurrency,
    });
    response = await fetch(`/api/dashboard/portfolio-performance?${params}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });
  }

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(
      errorData.error ||
        "Nu s-au putut încărca datele de performanță ale portofoliului"
    );
  }

  const result: PortfolioPerformanceResponse = await response.json();
  return result.data;
}

// Hook for fetching portfolio performance
export function usePortfolioPerformance(
  portfolioIds: string[],
  timePeriod: TimePeriod = "1W",
  displayCurrency: SupportedCurrency = "EUR"
) {
  return useQuery<PortfolioPerformanceData>({
    queryKey: portfolioPerformanceKeys.list(
      portfolioIds,
      timePeriod,
      displayCurrency
    ),
    queryFn: () =>
      fetchPortfolioPerformance(portfolioIds, timePeriod, displayCurrency),
    enabled: portfolioIds.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
    refetchOnWindowFocus: false,
    retry: (failureCount, error) => {
      // Don't retry on authentication errors
      if (error instanceof Error && error.message.includes("autentificat")) {
        return false;
      }
      // Retry up to 2 times for other errors
      return failureCount < 2;
    },
  });
}
