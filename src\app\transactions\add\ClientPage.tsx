"use client";

import { TransactionForm } from "@/components/transactions/transaction-form";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { TransactionFormData } from "@/lib/transaction-schemas";
import { Portfolio } from "@/utils/db/portfolio-queries";
import { AlertCircle, ArrowLeft, Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "sonner";

export default function AddTransactionClientPage() {
  const [portfolios, setPortfolios] = useState<Portfolio[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  // Load portfolios on component mount
  useEffect(() => {
    loadPortfolios();
  }, []);

  const loadPortfolios = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // First, ensure user has at least one portfolio
      const ensureResponse = await fetch("/api/portfolios/ensure", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!ensureResponse.ok) {
        throw new Error("Nu s-au putut încărca portofoliile");
      }

      const ensureData = await ensureResponse.json();
      setPortfolios(ensureData.portfolios);

      if (ensureData.defaultCreated) {
        toast.success("A fost creat un portofoliu implicit pentru tine!");
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "A apărut o eroare";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (data: TransactionFormData) => {
    try {
      setIsSubmitting(true);

      // Data is already in the correct string format (YYYY-MM-DD)
      const response = await fetch("/api/transactions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Nu s-a putut crea tranzacția");
      }

      await response.json();
      toast.success("Tranzacția a fost adăugată cu succes!");
      router.push("/portfolios");
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "A apărut o eroare";
      throw new Error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleGoBack = () => {
    router.back();
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="flex flex-col items-center gap-4">
            <Loader2 className="h-8 w-8 animate-spin text-portavio-orange" />
            <p className="text-muted-foreground">Se încarcă...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardContent className="p-6">
              <div className="flex flex-col items-center gap-4 text-center">
                <AlertCircle className="h-12 w-12 text-red-500" />
                <div>
                  <h2 className="text-xl font-semibold mb-2">
                    A apărut o eroare
                  </h2>
                  <p className="text-muted-foreground mb-4">{error}</p>
                  <div className="flex gap-2 justify-center">
                    <Button onClick={loadPortfolios} variant="outline">
                      Încearcă din nou
                    </Button>
                    <Button onClick={handleGoBack} variant="ghost">
                      <ArrowLeft className="h-4 w-4 mr-2" />
                      Înapoi
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <Button onClick={handleGoBack} variant="ghost" className="mb-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Înapoi
          </Button>

          <div className="text-center">
            <h1 className="text-3xl font-bold mb-2">Adaugă Tranzacție Nouă</h1>
            <p className="text-muted-foreground">
              Completează formularul pentru a adăuga o nouă tranzacție în
              portofoliul tău
            </p>
          </div>
        </div>

        {/* Transaction Form */}
        <TransactionForm
          portfolios={portfolios}
          onSubmit={handleSubmit}
          isLoading={isSubmitting}
          defaultPortfolioId={portfolios[0]?.id}
        />
      </div>
    </div>
  );
}
