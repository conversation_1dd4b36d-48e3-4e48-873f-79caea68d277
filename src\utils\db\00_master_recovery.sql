-- PORTAVIO DATABASE RECOVERY SCRIPT
-- This script recreates the complete database schema for the Portavio application
-- Run this script to recover from database loss or to set up a new environment

-- IMPORTANT: Run these scripts in order!
-- 1. Functions and triggers first (dependencies)
-- 2. Reference tables (no dependencies)
-- 3. Auth tables (no dependencies on app tables)
-- 4. Portfolio tables (depends on auth)
-- 5. Asset tables (depends on reference tables)
-- 6. Dividend tables (depends on asset tables)
-- 7. Application tables (depends on auth)

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create ptvuser schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS ptvuser;

-- EXECUTION ORDER:
-- \i src/utils/db/02_functions_triggers.sql
-- \i src/utils/db/01_auth_tables.sql
-- \i src/utils/db/03_reference_tables.sql
-- \i src/utils/db/04_portfolio_tables.sql
-- \i src/utils/db/05_asset_tables.sql
-- \i src/utils/db/06_dividend_tables.sql
-- \i src/utils/db/07_application_tables.sql
-- \i src/utils/db/08_mail_config_table.sql

-- SCHEMA SUMMARY:
-- 
-- Authentication (better-auth):
-- - user: User accounts
-- - session: User sessions
-- - account: OAuth and credential accounts
-- - verification: Email verification tokens
--
-- Reference Tables:
-- - ptvuser.currency: Currency reference data
-- - ptvuser.country: Country reference data
-- - ptvuser.sector: Business sector data
-- - ptvuser.industry: Industry data
-- - ptvuser.asset_type: Asset type data
-- - ptvuser.exchange: Stock exchange data
--
-- Portfolio Management:
-- - ptvuser.portfolios: User investment portfolios
-- - ptvuser.transactions: Buy/sell transactions
--
-- Asset Management:
-- - ptvuser.asset: Master asset table
-- - ptvuser.asset_prices: Historical price data
-- - ptvuser.asset_metrics: Asset metrics and ratios
--
-- Financial Data:
-- - ptvuser.dividend: Dividend payments
--
-- Application Features:
-- - ptvuser.profile_pictures: User profile pictures
-- - ptvuser.search_results: Search result cache
-- - ptvuser_contact_submissions: Contact form submissions
-- - support_faq: FAQ data
-- - ptvuser.mail_config: SMTP email configuration
--
-- RELATIONSHIPS:
-- - portfolios.user_id -> user.id
-- - transactions.portfolio_id -> portfolios.id
-- - asset.currency_id -> currency.currency_id
-- - asset.exchange_id -> exchange.exchange_id
-- - asset.sector_id -> sector.sector_id
-- - asset.industry_id -> industry.industry_id
-- - asset.country_id -> country.country_id
-- - asset.asset_type_id -> asset_type.asset_type_id
-- - asset_prices.asset_id -> asset.asset_id
-- - asset_metrics.asset_id -> asset.asset_id
-- - dividend.asset_id -> asset.asset_id
-- - profile_pictures.user_id -> user.id
--
-- NOTES:
-- - All tables have created_at and updated_at timestamps
-- - Updated_at columns are automatically maintained by triggers
-- - Proper indexes are created for performance
-- - Foreign key constraints ensure data integrity
-- - Check constraints validate data quality
