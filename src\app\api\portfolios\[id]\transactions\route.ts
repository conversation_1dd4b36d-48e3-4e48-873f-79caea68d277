import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import {
  getPortfolioById,
  getPortfolioTransactionsWithAssets,
  getPortfolioTransactionsCount,
} from "@/utils/db/portfolio-queries";

// GET /api/portfolios/[id]/transactions - Get transactions for a specific portfolio
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.user) {
      return NextResponse.json(
        { error: "Nu ești autentificat" },
        { status: 401 }
      );
    }

    const { id } = await params;
    const portfolioId = id;

    if (!portfolioId) {
      return NextResponse.json(
        { error: "ID-ul portofoliului este obligatoriu" },
        { status: 400 }
      );
    }

    // Verify that the portfolio exists and belongs to the user
    const portfolio = await getPortfolioById(portfolioId);
    if (!portfolio) {
      return NextResponse.json(
        { error: "Portofoliul nu a fost găsit" },
        { status: 404 }
      );
    }

    if (portfolio.user_id !== session.user.id) {
      return NextResponse.json(
        { error: "Nu ai permisiunea să accesezi acest portofoliu" },
        { status: 403 }
      );
    }

    // Get transactions with asset information and total count
    const [transactions, totalCount] = await Promise.all([
      getPortfolioTransactionsWithAssets(portfolioId),
      getPortfolioTransactionsCount(portfolioId),
    ]);

    return NextResponse.json({
      transactions,
      count: transactions.length,
      totalCount,
      portfolio: {
        id: portfolio.id,
        name: portfolio.name,
        description: portfolio.description,
      },
      message: "Tranzacțiile au fost încărcate cu succes",
    });
  } catch (error) {
    console.error("Error fetching portfolio transactions:", error);
    return NextResponse.json(
      { error: "Nu s-au putut încărca tranzacțiile" },
      { status: 500 }
    );
  }
}
