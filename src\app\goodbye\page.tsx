import { Check<PERSON><PERSON><PERSON>, Heart, Home } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import type { <PERSON>ada<PERSON> } from "next";

export const metadata: Metadata = {
  title: "La revedere - Portavio",
  description: "Contul tău Portavio a fost șters cu succes",
};

export default function GoodbyePage() {
  return (
    <div className="container mx-auto py-8 px-4 max-w-2xl">
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-bold mb-2 flex items-center justify-center gap-2">
          <Heart className="h-8 w-8 text-portavio-orange" />
          La revedere
        </h1>
        <p className="text-muted-foreground">
          Contul tău Portavio a fost șters cu succes
        </p>
      </div>

      <Card>
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <CheckCircle className="h-16 w-16 text-green-500" />
          </div>
          <CardTitle className="dark:text-green-400 text-green-700">
            Cont șters cu succes
          </CardTitle>
          <CardDescription className="text-base">
            Toate datele asociate contului tău au fost eliminate definitiv din sistemele noastre.
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          <div className="text-center space-y-4">
            <div className="p-6 bg-gradient-to-br from-portavio-orange/10 to-portavio-orange/5 border border-portavio-orange/20 rounded-lg">
              <h3 className="text-lg font-semibold mb-3 text-portavio-orange">
                Îți mulțumim pentru timpul petrecut cu noi!
              </h3>
              <p className="text-sm text-muted-foreground leading-relaxed">
                Sperăm că experiența ta cu Portavio a fost utilă și că platforma noastră 
                te-a ajutat să îți urmărești portofoliul de investiții. Deși ne pare rău 
                să te vedem plecând, respectăm decizia ta.
              </p>
            </div>

            <div className="p-4 bg-muted/50 rounded-lg">
              <h4 className="font-medium mb-2">Ce s-a întâmplat cu datele tale:</h4>
              <ul className="text-sm text-muted-foreground space-y-1 text-left">
                <li>✓ Toate informațiile personale au fost șterse</li>
                <li>✓ Datele portofoliului au fost eliminate</li>
                <li>✓ Setările și preferințele au fost șterse</li>
                <li>✓ Istoricul activității a fost șters</li>
              </ul>
            </div>

            <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
              <h4 className="font-medium mb-2 text-blue-800 dark:text-blue-200">
                Vrei să revii în viitor?
              </h4>
              <p className="text-sm text-blue-700 dark:text-blue-300">
                Poți crea oricând un cont nou pe Portavio. Vom fi bucuroși să te primim înapoi 
                în comunitatea noastră de investitori!
              </p>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-3 pt-4">
            <Button asChild className="flex-1">
              <Link href="/">
                <Home className="h-4 w-4 mr-2" />
                Înapoi la pagina principală
              </Link>
            </Button>
            <Button variant="outline" asChild className="flex-1">
              <Link href="/auth/signup">
                Creează un cont nou
              </Link>
            </Button>
          </div>

          <div className="text-center pt-4 border-t">
            <p className="text-xs text-muted-foreground">
              Dacă ai întrebări sau feedback, ne poți contacta oricând la{" "}
              <Link 
                href="/contact" 
                className="text-portavio-orange hover:underline"
              >
                <EMAIL>
              </Link>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
