"use client";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ChevronDown } from "lucide-react";
import { useState } from "react";

export type SupportedCurrency = "EUR" | "USD" | "RON";

interface CurrencyOption {
  code: SupportedCurrency;
  name: string;
  symbol: string;
  flag: string;
}

const CURRENCY_OPTIONS: CurrencyOption[] = [
  { code: "EUR", name: "Euro", symbol: "€", flag: "🇪🇺" },
  { code: "USD", name: "US Dollar", symbol: "$", flag: "🇺🇸" },
  { code: "RON", name: "Romanian Leu", symbol: "RON", flag: "🇷🇴" },
];

interface CurrencySelectorProps {
  selectedCurrency: SupportedCurrency;
  onCurrencyChange: (currency: SupportedCurrency) => void;
  disabled?: boolean;
}

export function CurrencySelector({
  selectedCurrency,
  onCurrencyChange,
  disabled = false,
}: CurrencySelectorProps) {
  const [isOpen, setIsOpen] = useState(false);

  const selectedOption = CURRENCY_OPTIONS.find(
    (option) => option.code === selectedCurrency
  );

  const handleCurrencySelect = (
    currency: SupportedCurrency,
    event?: React.MouseEvent
  ) => {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    onCurrencyChange(currency);
    setIsOpen(false);
  };

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          className="w-full min-w-[140px] justify-between"
          disabled={disabled}
        >
          <div className="flex items-center gap-2">
            <span className="font-medium">{selectedOption?.code}</span>
          </div>
          <div className="flex items-center gap-1">
            <Badge variant="secondary" className="text-xs">
              {selectedOption?.symbol}
            </Badge>
            <ChevronDown className="h-4 w-4" />
          </div>
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent className="w-56" align="end">
        <DropdownMenuLabel className="text-xs font-medium text-muted-foreground">
          Moneda de afișare
        </DropdownMenuLabel>
        <DropdownMenuSeparator />

        <div className="space-y-1 p-1">
          {CURRENCY_OPTIONS.map((option) => (
            <div
              key={option.code}
              className={`flex items-center justify-between p-2 rounded-sm cursor-pointer hover:bg-accent hover:text-accent-foreground transition-colors ${
                selectedCurrency === option.code
                  ? "bg-accent text-accent-foreground"
                  : ""
              }`}
              onClick={(e) => handleCurrencySelect(option.code, e)}
            >
              <div className="flex items-center gap-3">
                <span className="text-lg">{option.flag} </span>
                <div className="flex flex-col">
                  <span className="font-medium text-sm">{option.code}</span>
                  <span className="text-xs">{option.name}</span>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Badge
                  variant={
                    selectedCurrency === option.code ? "default" : "secondary"
                  }
                  className="text-xs"
                >
                  {option.symbol}
                </Badge>
                {selectedCurrency === option.code && (
                  <div className="w-2 h-2 bg-primary rounded-full" />
                )}
              </div>
            </div>
          ))}
        </div>

        <DropdownMenuSeparator />
        <div className="p-2">
          <p className="text-xs text-muted-foreground text-center">
            Toate valorile vor fi convertite în moneda selectată
          </p>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
