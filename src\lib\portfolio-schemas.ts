import { z } from "zod";

// Portfolio Edit Schema
export const portfolioEditSchema = z.object({
  name: z
    .string()
    .min(1, "Numele portofoliului este obligatoriu")
    .max(255, "Numele nu poate avea mai mult de 255 de caractere")
    .trim(),
  description: z
    .string()
    .max(1000, "Descrierea nu poate avea mai mult de 1000 de caractere")
    .trim()
    .optional()
    .or(z.literal("")),
  is_active: z.boolean({
    required_error: "Statusul portofoliului este obligatoriu",
    invalid_type_error: "Statusul portofoliului trebuie să fie valid",
  }),
});

// Portfolio Create Schema (for new portfolios)
export const portfolioCreateSchema = z.object({
  name: z
    .string()
    .min(1, "Numele portofoliului este obligatoriu")
    .max(255, "Numele nu poate avea mai mult de 255 de caractere")
    .trim(),
  description: z
    .string()
    .max(1000, "Descrierea nu poate avea mai mult de 1000 de caractere")
    .trim()
    .optional()
    .or(z.literal("")),
});

// Type definitions
export type PortfolioEditFormData = z.infer<typeof portfolioEditSchema>;
export type PortfolioCreateFormData = z.infer<typeof portfolioCreateSchema>;

// Default form data for editing
export function getDefaultPortfolioEditFormData(
  portfolio?: {
    name: string;
    description?: string | null;
    is_active: boolean;
  }
): PortfolioEditFormData {
  return {
    name: portfolio?.name || "",
    description: portfolio?.description || "",
    is_active: portfolio?.is_active ?? true,
  };
}

// Default form data for creating
export function getDefaultPortfolioCreateFormData(): PortfolioCreateFormData {
  return {
    name: "",
    description: "",
  };
}

// Portfolio status options for select component
export const portfolioStatusOptions = [
  { value: "true", label: "Activ", description: "Portofoliul este activ și vizibil" },
  { value: "false", label: "Inactiv", description: "Portofoliul este ascuns din listă" },
] as const;

// Helper function to convert string to boolean for form handling
export function stringToBoolean(value: string): boolean {
  return value === "true";
}

// Helper function to convert boolean to string for form handling
export function booleanToString(value: boolean): string {
  return value.toString();
}
