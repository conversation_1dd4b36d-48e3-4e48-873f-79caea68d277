"use client";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useDeletePortfolio } from "@/hooks/use-portfolios-query";
import { PortfolioWithMetrics } from "@/utils/db/portfolio-queries";
import { AlertTriangle, Loader2 } from "lucide-react";
import { toast } from "sonner";

interface DeletePortfolioDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  portfolio: PortfolioWithMetrics;
  onSuccess?: () => void;
}

export function DeletePortfolioDialog({
  open,
  onOpenChange,
  portfolio,
  onSuccess,
}: DeletePortfolioDialogProps) {
  const deletePortfolioMutation = useDeletePortfolio();

  const handleDelete = async () => {
    deletePortfolioMutation.mutate(portfolio.id, {
      onSuccess: () => {
        toast.success("Portofoliul a fost șters cu succes!");
        onOpenChange(false);
        onSuccess?.();
      },
      onError: (error) => {
        const errorMessage =
          error instanceof Error ? error.message : "A apărut o eroare";
        toast.error(errorMessage);
      },
    });
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen && !deletePortfolioMutation.isPending) {
      onOpenChange(newOpen);
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={handleOpenChange}>
      <AlertDialogContent className="sm:max-w-[425px]">
        <AlertDialogHeader>
          <div className="flex items-center gap-3">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20">
              <AlertTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
            </div>
            <div>
              <AlertDialogTitle className="text-left">
                Șterge portofoliul
              </AlertDialogTitle>
              <AlertDialogDescription className="text-left">
                Ești sigur că vrei să ștergi acest portofoliu?
              </AlertDialogDescription>
            </div>
          </div>
        </AlertDialogHeader>

        <div className="py-4">
          <div className="rounded-lg bg-muted p-4 mb-4">
            <h4 className="font-semibold text-sm mb-1">Portofoliu selectat:</h4>
            <p className="font-medium">{portfolio.name}</p>
            {portfolio.description && (
              <p className="text-sm text-muted-foreground mt-1">
                {portfolio.description}
              </p>
            )}
          </div>

          <div className="space-y-2 text-sm">
            <div className="flex items-center justify-between">
              <span className="text-muted-foreground">Poziții active:</span>
              <span className="font-medium">
                {portfolio.metrics.totalHoldings}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-muted-foreground">Total tranzacții:</span>
              <span className="font-medium">
                {portfolio.metrics.totalTransactions}
              </span>
            </div>
          </div>

          <div className="mt-4 p-3 rounded-lg bg-red-50 dark:bg-red-900/10 border border-red-200 dark:border-red-800">
            <p className="text-sm text-red-800 dark:text-red-200">
              <strong>Atenție:</strong> Toate tranzacțiile din acest portofoliu
              vor fi de asemenea șterse. Această acțiune nu poate fi anulată.
            </p>
          </div>
        </div>

        <AlertDialogFooter>
          <AlertDialogCancel disabled={deletePortfolioMutation.isPending}>
            Anulează
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={deletePortfolioMutation.isPending}
            className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
          >
            {deletePortfolioMutation.isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Se șterge...
              </>
            ) : (
              "Șterge"
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
