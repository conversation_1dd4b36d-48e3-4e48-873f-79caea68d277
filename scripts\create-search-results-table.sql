-- Create search_results table to cache EODHD API search results for improved performance
CREATE TABLE IF NOT EXISTS ptvuser.search_results (
    id SERIAL PRIMARY KEY,
    code VARCHAR(50) NOT NULL, -- Asset code/ticker (e.g., "AAPL", "AA")
    exchange VARCHAR(10) NOT NULL, -- Exchange code (e.g., "US", "LSE", "VN")
    name VARCHAR(500) NOT NULL, -- Asset name (e.g., "Apple Inc", "Alcoa Corp")
    type VARCHAR(100) NOT NULL, -- Asset type (e.g., "Common Stock", "ETF", "Currency")
    country VARCHAR(100) NOT NULL, -- Country (e.g., "USA", "UK", "Vietnam")
    currency VARCHAR(10) NOT NULL, -- Currency code (e.g., "USD", "GBX", "VND")
    isin VARCHAR(20), -- ISIN code (nullable since not all assets have ISIN)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Composite unique constraint to prevent duplicates (same code + exchange combination)
    CONSTRAINT search_results_code_exchange_unique UNIQUE (code, exchange)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_search_results_code ON ptvuser.search_results(code);
CREATE INDEX IF NOT EXISTS idx_search_results_exchange ON ptvuser.search_results(exchange);
CREATE INDEX IF NOT EXISTS idx_search_results_name ON ptvuser.search_results(name);
CREATE INDEX IF NOT EXISTS idx_search_results_type ON ptvuser.search_results(type);
CREATE INDEX IF NOT EXISTS idx_search_results_country ON ptvuser.search_results(country);
CREATE INDEX IF NOT EXISTS idx_search_results_currency ON ptvuser.search_results(currency);
CREATE INDEX IF NOT EXISTS idx_search_results_created_at ON ptvuser.search_results(created_at DESC);

-- Create updated_at trigger
CREATE TRIGGER update_search_results_updated_at
    BEFORE UPDATE ON ptvuser.search_results
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE ptvuser.search_results IS 'Cache table for EODHD API search results to improve search performance';
COMMENT ON COLUMN ptvuser.search_results.id IS 'Auto-incremented primary key';
COMMENT ON COLUMN ptvuser.search_results.code IS 'Asset code/ticker symbol';
COMMENT ON COLUMN ptvuser.search_results.exchange IS 'Exchange code where the asset is traded';
COMMENT ON COLUMN ptvuser.search_results.name IS 'Full name of the asset';
COMMENT ON COLUMN ptvuser.search_results.type IS 'Type of asset (Common Stock, ETF, Currency, etc.)';
COMMENT ON COLUMN ptvuser.search_results.country IS 'Country where the asset is based';
COMMENT ON COLUMN ptvuser.search_results.currency IS 'Currency in which the asset is traded';
COMMENT ON COLUMN ptvuser.search_results.isin IS 'International Securities Identification Number (nullable)';
COMMENT ON COLUMN ptvuser.search_results.created_at IS 'Timestamp when the record was first cached';
COMMENT ON COLUMN ptvuser.search_results.updated_at IS 'Timestamp when the record was last updated';
