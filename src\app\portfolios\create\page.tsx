import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { redirect } from "next/navigation";
import { PortfolioCreateClient } from "@/components/portfolios/portfolio-create-client";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Creează Portofoliu - Portavio",
  description: "Creează un nou portofoliu pentru a-ți urmări investițiile cu Portavio.",
  keywords: [
    "creează portofoliu",
    "nou portofoliu",
    "management portofoliu",
    "portavio",
    "investiții",
    "urmărire",
  ],
  openGraph: {
    title: "Creează Portofoliu - Portavio",
    description: "Creează un nou portofoliu pentru a-ți urmări investițiile cu Portavio.",
    type: "website",
  },
};

export default async function PortfolioCreatePage() {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    redirect("/auth/signin");
  }

  return (
    <div className="container mx-auto px-4 py-8 min-h-screen max-w-4xl">
      <PortfolioCreateClient />
    </div>
  );
}
