"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  getAccountCapabilities,
  type UserAccountData,
} from "@/lib/account-schemas";
import { deleteUser } from "@/lib/auth-client";
import { AlertTriangle, Eye, EyeOff, Loader2, Trash2 } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

interface DeleteAccountSectionProps {
  accountData?: UserAccountData | null;
}

export function DeleteAccountSection({
  accountData,
}: DeleteAccountSectionProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);

  const accountCapabilities = accountData
    ? getAccountCapabilities(accountData.accounts)
    : null;
  const requiresPassword = accountCapabilities?.canChangePassword ?? false;

  const handleDeleteAccount = async () => {
    if (requiresPassword && !password.trim()) {
      toast.error("Parola este obligatorie pentru ștergerea contului");
      return;
    }

    setIsDeleting(true);
    try {
      const deleteParams: any = {
        callbackURL: "/goodbye",
      };

      if (requiresPassword) {
        deleteParams.password = password;
      }

      const result = await deleteUser(deleteParams);
      // First: Check for success response
      if (
        result &&
        typeof result === "object" &&
        "data" in result &&
        result.data
      ) {
        const data = result.data as any;
        if (data.success === true) {
          toast.success(
            "Email de confirmare trimis! Verifică-ți căsuța de email pentru a finaliza ștergerea contului."
          );
          setPassword("");
          return;
        }
      }

      // Second: Check if the result contains an error
      if (
        result &&
        typeof result === "object" &&
        "error" in result &&
        result.error
      ) {
        const error = result.error as any;

        // Handle specific error codes
        if (error.code === "INVALID_PASSWORD") {
          toast.error(
            "Parola introdusă este incorectă. Te rugăm să încerci din nou."
          );
          return;
        } else if (error.code === "USER_NOT_FOUND") {
          toast.error(
            "Contul nu a fost găsit. Te rugăm să contactezi suportul."
          );
          return;
        } else if (error.code === "EMAIL_NOT_VERIFIED") {
          toast.error(
            "Adresa de email nu este verificată. Te rugăm să verifici email-ul înainte de a șterge contul."
          );
          return;
        } else {
          // Generic error for other error codes
          console.error("Delete account error:", error);
          toast.error(
            error.message ||
              "Eroare la ștergerea contului. Te rugăm să încerci din nou sau să contactezi suportul."
          );
          return;
        }
      }

      // Fallback: If we reach here, something unexpected happened
      console.error("Unexpected response format:", result);
      toast.error(
        "Răspuns neașteptat de la server. Te rugăm să încerci din nou sau să contactezi suportul."
      );
    } catch (error: any) {
      console.error("Delete account error:", error);

      // Handle network errors or other exceptions
      if (error?.code === "INVALID_PASSWORD") {
        toast.error(
          "Parola introdusă este incorectă. Te rugăm să încerci din nou."
        );
      } else if (error?.code === "USER_NOT_FOUND") {
        toast.error("Contul nu a fost găsit. Te rugăm să contactezi suportul.");
      } else if (error?.code === "EMAIL_NOT_VERIFIED") {
        toast.error(
          "Adresa de email nu este verificată. Te rugăm să verifici email-ul înainte de a șterge contul."
        );
      } else {
        toast.error(
          error?.message ||
            "Eroare la ștergerea contului. Te rugăm să încerci din nou sau să contactezi suportul."
        );
      }
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md p-4">
        <div className="flex items-start gap-3">
          <AlertTriangle className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5 flex-shrink-0" />
          <div>
            <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-1">
              Atenție: Această acțiune este ireversibilă
            </p>
            <p className="text-sm text-yellow-700 dark:text-yellow-300">
              După confirmarea din email, contul va fi șters definitiv și nu va
              putea fi recuperat.
            </p>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
          <p className="font-medium text-red-800 dark:text-red-200 mb-2">
            Următoarele date vor fi șterse permanent:
          </p>
          <ul className="text-sm text-red-700 dark:text-red-300 space-y-1">
            <li>• Toate informațiile personale</li>
            <li>• Datele portofoliului și investițiile urmărite</li>
            <li>• Setările și preferințele contului</li>
            <li>• Istoricul activității și tranzacțiilor</li>
          </ul>
        </div>

        {requiresPassword && (
          <div className="space-y-2">
            <Label htmlFor="delete-password" className="text-sm font-medium">
              Confirmă parola pentru a continua
            </Label>
            <div className="relative">
              <Input
                id="delete-password"
                type={showPassword ? "text" : "password"}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Introdu parola curentă"
                className="pr-10"
                disabled={isDeleting}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
                disabled={isDeleting}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4 text-gray-400" />
                ) : (
                  <Eye className="h-4 w-4 text-gray-400" />
                )}
              </button>
            </div>
          </div>
        )}

        <div className="flex justify-end">
          <Button
            onClick={handleDeleteAccount}
            disabled={isDeleting || (requiresPassword && !password.trim())}
            variant="destructive"
            className="bg-red-600 hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700"
          >
            {isDeleting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Se procesează...
              </>
            ) : (
              <>
                <Trash2 className="mr-2 h-4 w-4" />
                Șterge contul
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}
