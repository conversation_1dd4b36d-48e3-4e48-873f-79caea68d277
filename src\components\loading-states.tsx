"use client";

import { LoadingSpinner } from "@/components/ui/loading-spinner";
import {
  Skeleton,
  SkeletonAvatar,
  SkeletonCard,
} from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import * as React from "react";

// Main page loading component
export function PageLoading({
  className,
  message = "Se încarcă...",
  ...props
}: React.HTMLAttributes<HTMLDivElement> & { message?: string }) {
  return (
    <div
      className={cn(
        "flex flex-col items-center justify-center min-h-[400px] space-y-4",
        className
      )}
      {...props}
    >
      <LoadingSpinner size="xl" variant="portavio" />
      <p className="text-muted-foreground text-lg font-medium">{message}</p>
    </div>
  );
}

// Profile page skeleton
export function ProfilePageSkeleton({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div className={cn("min-h-screen bg-background", className)} {...props}>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          {/* Header skeleton */}
          <div className="flex justify-between items-center mb-8 flex-wrap gap-4">
            <div>
              <Skeleton className="h-12 w-64 mb-2" />
            </div>
            <Skeleton className="h-10 w-32" />
          </div>

          <div className="flex flex-col lg:flex-row gap-0">
            {/* Sidebar skeleton */}
            <div className="lg:w-80 bg-card border-r">
              <div className="p-6 space-y-4">
                <div className="flex items-center gap-3">
                  <SkeletonAvatar className="h-12 w-12" />
                  <div className="space-y-1">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-3 w-32" />
                  </div>
                </div>
                <div className="space-y-2">
                  {Array.from({ length: 4 }).map((_, i) => (
                    <Skeleton key={i} className="h-10 w-full" />
                  ))}
                </div>
              </div>
            </div>

            {/* Main content skeleton */}
            <div className="flex-1 p-6">
              <div className="space-y-6">
                <Skeleton className="h-8 w-48" />
                <SkeletonCard />
                <SkeletonCard />
                <SkeletonCard />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Auth form skeleton
export function AuthFormSkeleton({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn(
        "flex items-center justify-center min-h-screen bg-background",
        className
      )}
      {...props}
    >
      <div className="w-full max-w-md space-y-6 p-6">
        <div className="text-center space-y-2">
          <Skeleton className="h-8 w-48 mx-auto" />
          <Skeleton className="h-4 w-64 mx-auto" />
        </div>

        <div className="space-y-4">
          <div className="space-y-2">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-10 w-full" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-10 w-full" />
          </div>
          <Skeleton className="h-10 w-full" />
        </div>

        <div className="text-center">
          <Skeleton className="h-4 w-40 mx-auto" />
        </div>
      </div>
    </div>
  );
}

// Support page skeleton
export function SupportPageSkeleton({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn("container mx-auto px-6 py-12 max-w-4xl", className)}
      {...props}
    >
      <div className="text-center mb-12 space-y-4">
        <Skeleton className="h-9 w-32 mx-auto" />
        <Skeleton className="h-6 w-96 mx-auto" />
      </div>

      <div className="space-y-4">
        {Array.from({ length: 6 }).map((_, i) => (
          <div key={i} className="border rounded-lg p-4">
            <div className="flex justify-between items-center">
              <Skeleton className="h-5 w-3/4" />
              <Skeleton className="h-5 w-5" />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// Contact page skeleton
export function ContactPageSkeleton({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn("container mx-auto px-6 py-12 max-w-2xl", className)}
      {...props}
    >
      <div className="text-center mb-12 space-y-4">
        <Skeleton className="h-9 w-40 mx-auto" />
        <Skeleton className="h-6 w-80 mx-auto" />
      </div>

      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-10 w-full" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-10 w-full" />
          </div>
        </div>
        <div className="space-y-2">
          <Skeleton className="h-4 w-16" />
          <Skeleton className="h-10 w-full" />
        </div>
        <div className="space-y-2">
          <Skeleton className="h-4 w-20" />
          <Skeleton className="h-32 w-full" />
        </div>
        <Skeleton className="h-10 w-32" />
      </div>
    </div>
  );
}

// Generic content loading
export function ContentLoading({
  className,
  message = "Se încarcă conținutul...",
  ...props
}: React.HTMLAttributes<HTMLDivElement> & { message?: string }) {
  return (
    <div
      className={cn(
        "flex flex-col items-center justify-center py-12 space-y-4",
        className
      )}
      {...props}
    >
      <LoadingSpinner size="lg" variant="portavio" />
      <p className="text-muted-foreground">{message}</p>
    </div>
  );
}
