"use client";

import { Column } from "@tanstack/react-table";
import { ArrowDown, ArrowUp, ChevronsUpDown } from "lucide-react";
import { cn } from "@/lib/utils";

interface DataTableColumnHeaderProps<TData, TValue>
  extends React.HTMLAttributes<HTMLDivElement> {
  column: Column<TData, TValue>;
  title: string;
  isFirstColumn?: boolean;
}

export function DataTableColumnHeader<TData, TValue>({
  column,
  title,
  className,
  isFirstColumn = false,
}: DataTableColumnHeaderProps<TData, TValue>) {
  if (!column.getCanSort()) {
    return (
      <div
        className={cn(
          "w-full",
          isFirstColumn ? "text-left" : "text-right",
          className
        )}
      >
        {title}
      </div>
    );
  }

  return (
    <div
      className={cn(
        "flex items-center w-full gap-2 cursor-pointer select-none hover:text-foreground/80 transition-colors",
        isFirstColumn ? "justify-start" : "justify-end",
        className
      )}
      onClick={() => column.toggleSorting()}
    >
      <span className="whitespace-nowrap">{title}</span>
      {column.getIsSorted() === "desc" ? (
        <ArrowDown className="h-4 w-4 flex-shrink-0" />
      ) : column.getIsSorted() === "asc" ? (
        <ArrowUp className="h-4 w-4 flex-shrink-0" />
      ) : (
        <ChevronsUpDown className="h-4 w-4 opacity-50 flex-shrink-0" />
      )}
    </div>
  );
}
