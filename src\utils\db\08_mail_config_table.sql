-- Mail Configuration Table
-- Table for storing SMTP email configuration

-- Create mail_config table
CREATE TABLE IF NOT EXISTS ptvuser.mail_config (
    config_id SERIAL PRIMARY KEY,
    config_name VARCHAR(100) NOT NULL,
    smtp_host VARCHAR(255) NOT NULL,
    smtp_port INTEGER NOT NULL DEFAULT 587,
    smtp_user VARCHAR(255) NOT NULL,
    smtp_pass VARCHAR(255) NOT NULL,
    smtp_secure BOOLEAN NOT NULL DEFAULT false,
    is_default BOOLEAN NOT NULL DEFAULT false,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Ensure only one default configuration
    CONSTRAINT mail_config_single_default EXCLUDE (is_default WITH =) WHERE (is_default = true),
    -- Validate SMTP port range
    CONSTRAINT mail_config_port_range CHECK (smtp_port > 0 AND smtp_port <= 65535),
    -- Validate required fields are not empty
    CONSTRAINT mail_config_name_check CHECK (LENGTH(TRIM(config_name)) > 0),
    CONSTRAINT mail_config_host_check CHECK (LENGTH(TRIM(smtp_host)) > 0),
    CONSTRAINT mail_config_user_check CHECK (LENGTH(TRIM(smtp_user)) > 0),
    CONSTRAINT mail_config_pass_check CHECK (LENGTH(TRIM(smtp_pass)) > 0)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_mail_config_is_default ON ptvuser.mail_config(is_default);
CREATE INDEX IF NOT EXISTS idx_mail_config_is_active ON ptvuser.mail_config(is_active);
CREATE INDEX IF NOT EXISTS idx_mail_config_name ON ptvuser.mail_config(config_name);

-- Create updated_at trigger
CREATE TRIGGER update_mail_config_updated_at
    BEFORE UPDATE ON ptvuser.mail_config
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE ptvuser.mail_config IS 'SMTP email configuration settings';
COMMENT ON COLUMN ptvuser.mail_config.config_id IS 'Unique identifier for the mail configuration';
COMMENT ON COLUMN ptvuser.mail_config.config_name IS 'Descriptive name for this configuration';
COMMENT ON COLUMN ptvuser.mail_config.smtp_host IS 'SMTP server hostname';
COMMENT ON COLUMN ptvuser.mail_config.smtp_port IS 'SMTP server port (usually 587 for TLS, 465 for SSL, 25 for plain)';
COMMENT ON COLUMN ptvuser.mail_config.smtp_user IS 'SMTP authentication username';
COMMENT ON COLUMN ptvuser.mail_config.smtp_pass IS 'SMTP authentication password';
COMMENT ON COLUMN ptvuser.mail_config.smtp_secure IS 'Whether to use SSL/TLS encryption';
COMMENT ON COLUMN ptvuser.mail_config.is_default IS 'Whether this is the default configuration to use';
COMMENT ON COLUMN ptvuser.mail_config.is_active IS 'Whether this configuration is active';

-- Insert the default mail configuration with your credentials
INSERT INTO ptvuser.mail_config (
    config_name,
    smtp_host,
    smtp_port,
    smtp_user,
    smtp_pass,
    smtp_secure,
    is_default,
    is_active
) VALUES (
    'Portavio Default SMTP',
    'blizzard.mxrouting.net',
    587,  -- Standard SMTP port for TLS
    '<EMAIL>',
    'Zfg4vbWvRfD32nua8jB8',
    false,  -- Use STARTTLS instead of SSL
    true,   -- This is the default configuration
    true    -- Configuration is active
) ON CONFLICT DO NOTHING;
