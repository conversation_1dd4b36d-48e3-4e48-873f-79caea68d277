# Database Recovery Scripts

This directory contains SQL scripts to completely reconstruct the Portavio database schema after accidental data loss.

## 🚨 CRITICAL RECOVERY SITUATION

These scripts were created to recover from a database drop incident. They reconstruct the complete schema based on analysis of the application codebase.

## Files Overview

- `00_master_recovery.sql` - Master script with execution order and schema documentation
- `01_auth_tables.sql` - Better-auth authentication tables
- `02_functions_triggers.sql` - Database functions and triggers
- `03_reference_tables.sql` - Reference lookup tables (currency, country, etc.)
- `04_portfolio_tables.sql` - Portfolio and transaction tables
- `05_asset_tables.sql` - Asset, price, and metrics tables
- `06_dividend_tables.sql` - Dividend data tables
- `07_application_tables.sql` - Application-specific tables
- `99_validation_script.sql` - Schema validation script

## Recovery Instructions

### 1. Prerequisites

- PostgreSQL database server running
- Database user with CREATE privileges
- Access to psql command line or database administration tool

### 2. Execute Scripts in Order

**IMPORTANT**: Scripts must be executed in the exact order shown below due to dependencies.

```bash
# Connect to your database
psql -h your_host -U your_user -d your_database

# Execute scripts in order:
\i src/utils/db/02_functions_triggers.sql
\i src/utils/db/01_auth_tables.sql
\i src/utils/db/03_reference_tables.sql
\i src/utils/db/04_portfolio_tables.sql
\i src/utils/db/05_asset_tables.sql
\i src/utils/db/06_dividend_tables.sql
\i src/utils/db/07_application_tables.sql
\i src/utils/db/08_mail_config_table.sql

# Validate the schema
\i src/utils/db/99_validation_script.sql
```

### 3. Alternative: Single Command Execution

```bash
# Execute all scripts in one command
cat src/utils/db/02_functions_triggers.sql \
    src/utils/db/01_auth_tables.sql \
    src/utils/db/03_reference_tables.sql \
    src/utils/db/04_portfolio_tables.sql \
    src/utils/db/05_asset_tables.sql \
    src/utils/db/06_dividend_tables.sql \
    src/utils/db/07_application_tables.sql \
    src/utils/db/08_mail_config_table.sql | \
psql -h your_host -U your_user -d your_database
```

## Schema Overview

### Authentication Tables (Better-Auth)

- `user` - User accounts
- `session` - User sessions
- `account` - OAuth and credential accounts
- `verification` - Email verification tokens

### Reference Tables

- `ptvuser.currency` - Currency data (USD, EUR, RON, etc.)
- `ptvuser.country` - Country data
- `ptvuser.sector` - Business sectors
- `ptvuser.industry` - Industries
- `ptvuser.asset_type` - Asset types (Stock, ETF, etc.)
- `ptvuser.exchange` - Stock exchanges

### Portfolio Management

- `ptvuser.portfolios` - User investment portfolios
- `ptvuser.transactions` - Buy/sell transactions with fees

### Asset Management

- `ptvuser.asset` - Master asset table
- `ptvuser.asset_prices` - Historical price data
- `ptvuser.asset_metrics` - Asset metrics (52-week high/low, dividend yield)

### Financial Data

- `ptvuser.dividend` - Dividend payments

### Application Features

- `ptvuser.profile_pictures` - User profile pictures (base64)
- `ptvuser.search_results` - EODHD API search cache
- `ptvuser_contact_submissions` - Contact form submissions
- `support_faq` - FAQ data with default content
- `ptvuser.mail_config` - SMTP email configuration with default settings

## Key Features

✅ **Complete Schema Recovery** - All tables, columns, and relationships reconstructed
✅ **Data Integrity** - Foreign key constraints and check constraints included
✅ **Performance Optimized** - Proper indexes created
✅ **Audit Trail** - created_at/updated_at timestamps with automatic triggers
✅ **Validation** - Comprehensive validation script included
✅ **Email Configuration** - Default SMTP settings pre-configured

## Post-Recovery Steps

1. **Verify Schema**: Run the validation script to ensure all tables exist
2. **Populate Reference Data**: Add currency, country, sector data as needed
3. **Test Application**: Verify the application connects and functions properly
4. **Backup**: Create a backup of the recovered schema immediately

## Notes

- All tables use `IF NOT EXISTS` to prevent errors on re-execution
- The `ptvuser` schema is created automatically
- UUID extension is enabled for portfolio/transaction IDs
- Default FAQ content is inserted automatically
- All foreign key relationships are properly established

## Troubleshooting

If you encounter errors:

1. Check that scripts are executed in the correct order
2. Verify database user has sufficient privileges
3. Ensure PostgreSQL version supports all features used
4. Check the validation script output for missing components

## Contact

If you need assistance with the recovery process, refer to the application documentation or contact the development team.
