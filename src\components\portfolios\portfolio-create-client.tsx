"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { PortfolioCreateForm } from "@/components/portfolios/portfolio-create-form";
import { PortfolioCreateFormData } from "@/lib/portfolio-schemas";
import { ArrowLeft, AlertCircle, Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";

export function PortfolioCreateClient() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (data: PortfolioCreateFormData) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch("/api/portfolios", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Nu s-a putut crea portofoliul");
      }

      await response.json();

      // Show success message
      toast.success("Portofoliul a fost creat cu succes!");

      // Navigate back to portfolios page
      router.push("/portfolios");
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "A apărut o eroare";
      setError(errorMessage);
      throw err; // Re-throw to let the form handle it
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoBack = () => {
    router.back();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={handleGoBack}
          className="gap-2"
          disabled={isLoading}
        >
          <ArrowLeft className="h-4 w-4" />
          Înapoi
        </Button>
        <div>
          <h1 className="text-2xl font-bold">Creează Portofoliu Nou</h1>
          <p className="text-muted-foreground">
            Adaugă un nou portofoliu pentru a-ți urmări investițiile
          </p>
        </div>
      </div>

      {/* Global Error */}
      {error && (
        <div className="rounded-md bg-red-50 p-4 text-sm text-red-600 dark:text-red-400 dark:bg-red-900/20 flex items-center gap-2">
          <AlertCircle className="h-4 w-4" />
          <div>
            <p className="font-medium">A apărut o eroare</p>
            <p>{error}</p>
          </div>
        </div>
      )}

      {/* Loading State */}
      {isLoading && (
        <div className="flex items-center justify-center py-8">
          <div className="flex flex-col items-center gap-4">
            <Loader2 className="h-8 w-8 animate-spin text-portavio-orange" />
            <p className="text-muted-foreground">Se creează portofoliul...</p>
          </div>
        </div>
      )}

      {/* Create Form */}
      <PortfolioCreateForm onSubmit={handleSubmit} isLoading={isLoading} />

      {/* Info Section */}
      <div className="bg-muted/50 rounded-lg p-4 text-sm text-muted-foreground w-full max-w-2xl mx-auto">
        <h3 className="font-medium text-lg mb-2">Despre Portofolii</h3>
        <div className="space-y-2">
          <p>
            <span className="font-medium">Portofoliile</span> îți permit să
            organizezi și să urmărești diferite strategii de investiții sau
            categorii de active.
          </p>
          <p>
            <span className="font-medium">Exemple:</span> &quot;Portofoliu
            Principal&quot;, &quot;Investiții pe Termen Lung&quot;,
            &quot;Acțiuni Românești&quot;, &quot;ETF-uri Internaționale&quot;
          </p>
          <p>
            <span className="font-medium">Beneficii:</span> Analiză separată a
            performanței, organizare mai bună a tranzacțiilor, rapoarte
            detaliate per portofoliu.
          </p>
        </div>
      </div>
    </div>
  );
}
