import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import {
  getPortfolioById,
  getPortfolioSellableTickers,
} from "@/utils/db/portfolio-queries";
import { headers } from "next/headers";

// params are async
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Get the authenticated session
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.user) {
      return NextResponse.json(
        { error: "Nu ești autentificat" },
        { status: 401 }
      );
    }

    const portfolioId = (await params).id;

    if (!portfolioId) {
      return NextResponse.json(
        { error: "ID-ul portofoliului este obligatoriu" },
        { status: 400 }
      );
    }

    // Verify portfolio exists and belongs to user
    const portfolio = await getPortfolioById(portfolioId);

    if (!portfolio) {
      return NextResponse.json(
        { error: "Portofoliul nu a fost găsit" },
        { status: 404 }
      );
    }

    if (portfolio.user_id !== session.user.id) {
      return NextResponse.json(
        { error: "Nu ai permisiunea să accesezi acest portofoliu" },
        { status: 403 }
      );
    }

    // Get sellable tickers from portfolio (tickers with positive holdings)
    const tickers = await getPortfolioSellableTickers(portfolioId);

    return NextResponse.json({
      tickers,
      count: tickers.length,
      portfolio: {
        id: portfolio.id,
        name: portfolio.name,
      },
      message: "Simbolurile au fost încărcate cu succes",
    });
  } catch (error) {
    console.error("Error fetching portfolio tickers:", error);
    const errorMessage =
      error instanceof Error ? error.message : "A apărut o eroare";

    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
