-- Reference Lookup Tables
-- These tables store reference data for assets (currencies, countries, sectors, etc.)

-- Create ptvuser schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS ptvuser;

-- Create currency table
CREATE TABLE IF NOT EXISTS ptvuser.currency (
    currency_id SERIAL PRIMARY KEY,
    code VARCHAR(10) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    symbol VARCHAR(10),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create country table
CREATE TABLE IF NOT EXISTS ptvuser.country (
    country_id SERIAL PRIMARY KEY,
    code VARCHAR(10) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create sector table
CREATE TABLE IF NOT EXISTS ptvuser.sector (
    sector_id SERIAL PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL UNIQUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create industry table
CREATE TABLE IF NOT EXISTS ptvuser.industry (
    industry_id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create asset_type table
CREATE TABLE IF NOT EXISTS ptvuser.asset_type (
    asset_type_id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create exchange table
CREATE TABLE IF NOT EXISTS ptvuser.exchange (
    exchange_id SERIAL PRIMARY KEY,
    code VARCHAR(10) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    country_id INTEGER REFERENCES ptvuser.country(country_id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_currency_code ON ptvuser.currency(code);
CREATE INDEX IF NOT EXISTS idx_country_code ON ptvuser.country(code);
CREATE INDEX IF NOT EXISTS idx_sector_name ON ptvuser.sector(name);
CREATE INDEX IF NOT EXISTS idx_industry_name ON ptvuser.industry(name);
CREATE INDEX IF NOT EXISTS idx_asset_type_name ON ptvuser.asset_type(name);
CREATE INDEX IF NOT EXISTS idx_exchange_code ON ptvuser.exchange(code);
CREATE INDEX IF NOT EXISTS idx_exchange_country_id ON ptvuser.exchange(country_id);

-- Create updated_at triggers
CREATE TRIGGER update_currency_updated_at
    BEFORE UPDATE ON ptvuser.currency
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_country_updated_at
    BEFORE UPDATE ON ptvuser.country
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_sector_updated_at
    BEFORE UPDATE ON ptvuser.sector
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_industry_updated_at
    BEFORE UPDATE ON ptvuser.industry
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_asset_type_updated_at
    BEFORE UPDATE ON ptvuser.asset_type
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_exchange_updated_at
    BEFORE UPDATE ON ptvuser.exchange
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE ptvuser.currency IS 'Currency reference data (USD, EUR, RON, etc.)';
COMMENT ON TABLE ptvuser.country IS 'Country reference data';
COMMENT ON TABLE ptvuser.sector IS 'Business sector reference data (Technology, Healthcare, etc.)';
COMMENT ON TABLE ptvuser.industry IS 'Industry reference data (Software, Pharmaceuticals, etc.)';
COMMENT ON TABLE ptvuser.asset_type IS 'Asset type reference data (Stock, ETF, Bond, etc.)';
COMMENT ON TABLE ptvuser.exchange IS 'Stock exchange reference data (NYSE, NASDAQ, etc.)';
