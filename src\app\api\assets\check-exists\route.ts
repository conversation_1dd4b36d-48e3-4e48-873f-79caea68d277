import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { checkAssetExists } from "@/lib/asset-existence-check";

// Validation schema for query parameters
const checkExistsParamsSchema = z.object({
  ticker: z
    .string()
    .min(1, "Simbolul este obligatoriu")
    .max(20, "Simbolul nu poate avea mai mult de 20 de caractere"),
});

// Response types
interface CheckExistsSuccessResponse {
  success: true;
  exists: boolean;
  ticker: string;
}

interface CheckExistsErrorResponse {
  success: false;
  error: string;
  code?: string;
}

// GET /api/assets/check-exists - Check if a ticker exists in the database
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const ticker = searchParams.get("ticker");

    // Validate parameters
    let validatedParams;
    try {
      validatedParams = checkExistsParamsSchema.parse({ ticker });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: "Parametri invalizi",
            code: "VALIDATION_ERROR",
          } as CheckExistsErrorResponse,
          { status: 400 }
        );
      }
      throw error;
    }

    console.log(`Checking asset existence for ticker: ${validatedParams.ticker}`);

    // Check if asset exists
    const exists = await checkAssetExists(validatedParams.ticker);

    const successResponse: CheckExistsSuccessResponse = {
      success: true,
      exists,
      ticker: validatedParams.ticker.toUpperCase(),
    };

    return NextResponse.json(successResponse);

  } catch (error) {
    console.error("Error in asset existence check API:", error);

    return NextResponse.json(
      {
        success: false,
        error: "A apărut o eroare la verificarea existenței activului",
        code: "INTERNAL_ERROR",
      } as CheckExistsErrorResponse,
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    {
      success: false,
      error: "Metodă nepermisă. Folosește GET pentru verificarea existenței",
      code: "METHOD_NOT_ALLOWED",
    } as CheckExistsErrorResponse,
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    {
      success: false,
      error: "Metodă nepermisă. Folosește GET pentru verificarea existenței",
      code: "METHOD_NOT_ALLOWED",
    } as CheckExistsErrorResponse,
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    {
      success: false,
      error: "Metodă nepermisă. Folosește GET pentru verificarea existenței",
      code: "METHOD_NOT_ALLOWED",
    } as CheckExistsErrorResponse,
    { status: 405 }
  );
}
