import { hasuraQuery } from "./hasura";

// Asset Types
export interface Asset {
  asset_id: number;
  ticker: string;
  name: string;
  company: string;
  isin?: string;
  currency_id?: string;
  exchange_id?: string;
  sector_id?: string;
  industry_id?: string;
  country_id?: string;
  asset_type_id?: string;
  logo_url?: string;
  created_at: string;
  updated_at: string;
}

export interface AssetSearchResult {
  asset_id: number;
  ticker: string;
  name: string;
  company: string;
  logo_url?: string;
}

// GraphQL Queries

/**
 * Search assets by ticker, name, or company
 */
export const SEARCH_ASSETS = `
  query SearchAssets($searchTerm: String!, $limit: Int!) {
    ptvuser_asset(
      where: {
        _or: [
          { ticker: { _ilike: $searchTerm } },
          { name: { _ilike: $searchTerm } },
          { company: { _ilike: $searchTerm } }
        ]
      }
      order_by: [
        { ticker: asc },
        { name: asc }
      ]
      limit: $limit
    ) {
      asset_id
      ticker
      name
      company
      logo_url
    }
  }
`;

/**
 * Get asset by ticker (exact match)
 */
export const GET_ASSET_BY_TICKER = `
  query GetAssetByTicker($ticker: String!) {
    ptvuser_asset(
      where: { ticker: { _eq: $ticker } }
      limit: 1
    ) {
      asset_id
      ticker
      name
      company
      isin
      currency_id
      exchange_id
      sector_id
      industry_id
      country_id
      asset_type_id
      logo_url
      created_at
      updated_at
    }
  }
`;

/**
 * Get asset by ID
 */
export const GET_ASSET_BY_ID = `
  query GetAssetById($assetId: Int!) {
    ptvuser_asset_by_pk(asset_id: $assetId) {
      asset_id
      ticker
      name
      company
      isin
      currency_id
      exchange_id
      sector_id
      industry_id
      country_id
      asset_type_id
      logo_url
      created_at
      updated_at
    }
  }
`;

// Utility Functions

/**
 * Search for assets by ticker, name, or company
 */
export async function searchAssets(
  searchTerm: string,
  limit: number = 15
): Promise<AssetSearchResult[]> {
  try {
    // Add wildcards for partial matching
    const searchPattern = `%${searchTerm}%`;

    const result = await hasuraQuery<{
      ptvuser_asset: AssetSearchResult[];
    }>(SEARCH_ASSETS, {
      variables: {
        searchTerm: searchPattern,
        limit,
      },
    });

    return result.ptvuser_asset || [];
  } catch (error) {
    console.error("Error searching assets:", error);
    throw new Error("Nu s-au putut căuta activele");
  }
}

/**
 * Get asset by ticker (exact match)
 */
export async function getAssetByTicker(ticker: string): Promise<Asset | null> {
  try {
    const result = await hasuraQuery<{
      ptvuser_asset: Asset[];
    }>(GET_ASSET_BY_TICKER, {
      variables: { ticker: ticker.toUpperCase() },
    });

    return result.ptvuser_asset?.[0] || null;
  } catch (error) {
    console.error("Error fetching asset by ticker:", error);
    throw new Error("Nu s-a putut încărca activul");
  }
}

/**
 * Get asset by ID
 */
export async function getAssetById(assetId: number): Promise<Asset | null> {
  try {
    const result = await hasuraQuery<{
      ptvuser_asset_by_pk: Asset | null;
    }>(GET_ASSET_BY_ID, {
      variables: { assetId },
    });

    return result.ptvuser_asset_by_pk;
  } catch (error) {
    console.error("Error fetching asset by ID:", error);
    throw new Error("Nu s-a putut încărca activul");
  }
}
